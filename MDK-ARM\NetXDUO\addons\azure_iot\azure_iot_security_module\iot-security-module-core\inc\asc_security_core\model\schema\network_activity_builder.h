#ifndef NETWORK_ACTIVITY_BUILDER_H
#define NETWORK_ACTIVITY_BUILDER_H

/* Generated by flatcc 0.6.1-dev <PERSON>Buffers schema compiler for C by dvide.com */

#ifndef NETWORK_ACTIVITY_READER_H
#include "network_activity_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#ifndef PROTOCOL_BUILDER_H
#include "protocol_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

#define __AzureIoTSecurity_IPv4Addresses_formal_args , uint32_t v0, uint32_t v1
#define __AzureIoTSecurity_IPv4Addresses_call_args , v0, v1
static inline AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_assign(AzureIoTSecurity_IPv4Addresses_t *p, uint32_t v0, uint32_t v1)
{ p->local_address = v0; p->remote_address = v1;
  return p; }
static inline AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_copy(AzureIoTSecurity_IPv4Addresses_t *p, const AzureIoTSecurity_IPv4Addresses_t *p2)
{ p->local_address = p2->local_address; p->remote_address = p2->remote_address;
  return p; }
static inline AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_assign_to_pe(AzureIoTSecurity_IPv4Addresses_t *p, uint32_t v0, uint32_t v1)
{ flatbuffers_uint32_assign_to_pe(&p->local_address, v0); flatbuffers_uint32_assign_to_pe(&p->remote_address, v1);
  return p; }
static inline AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_copy_to_pe(AzureIoTSecurity_IPv4Addresses_t *p, const AzureIoTSecurity_IPv4Addresses_t *p2)
{ flatbuffers_uint32_copy_to_pe(&p->local_address, &p2->local_address); flatbuffers_uint32_copy_to_pe(&p->remote_address, &p2->remote_address);
  return p; }
static inline AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_assign_from_pe(AzureIoTSecurity_IPv4Addresses_t *p, uint32_t v0, uint32_t v1)
{ flatbuffers_uint32_assign_from_pe(&p->local_address, v0); flatbuffers_uint32_assign_from_pe(&p->remote_address, v1);
  return p; }
static inline AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_copy_from_pe(AzureIoTSecurity_IPv4Addresses_t *p, const AzureIoTSecurity_IPv4Addresses_t *p2)
{ flatbuffers_uint32_copy_from_pe(&p->local_address, &p2->local_address); flatbuffers_uint32_copy_from_pe(&p->remote_address, &p2->remote_address);
  return p; }
__flatbuffers_build_struct(flatbuffers_, AzureIoTSecurity_IPv4Addresses, 8, 4, AzureIoTSecurity_IPv4Addresses_file_identifier, AzureIoTSecurity_IPv4Addresses_type_identifier)
__flatbuffers_define_fixed_array_primitives(flatbuffers_, AzureIoTSecurity_IPv4Addresses, AzureIoTSecurity_IPv4Addresses_t)

#define __AzureIoTSecurity_IPv6Addresses_formal_args , const uint32_t v0[4], const uint32_t v1[4]
#define __AzureIoTSecurity_IPv6Addresses_call_args , v0, v1
static inline AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_assign(AzureIoTSecurity_IPv6Addresses_t *p, const uint32_t v0[4], const uint32_t v1[4])
{ flatbuffers_uint32_array_copy(p->local_address, v0, 4); flatbuffers_uint32_array_copy(p->remote_address, v1, 4);
  return p; }
static inline AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_copy(AzureIoTSecurity_IPv6Addresses_t *p, const AzureIoTSecurity_IPv6Addresses_t *p2)
{ flatbuffers_uint32_array_copy(p->local_address, p2->local_address, 4); flatbuffers_uint32_array_copy(p->remote_address, p2->remote_address, 4);
  return p; }
static inline AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_assign_to_pe(AzureIoTSecurity_IPv6Addresses_t *p, const uint32_t v0[4], const uint32_t v1[4])
{ flatbuffers_uint32_array_copy_to_pe(p->local_address, v0, 4); flatbuffers_uint32_array_copy_to_pe(p->remote_address, v1, 4);
  return p; }
static inline AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_copy_to_pe(AzureIoTSecurity_IPv6Addresses_t *p, const AzureIoTSecurity_IPv6Addresses_t *p2)
{ flatbuffers_uint32_array_copy_to_pe(p->local_address, p2->local_address, 4); flatbuffers_uint32_array_copy_to_pe(p->remote_address, p2->remote_address, 4);
  return p; }
static inline AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_assign_from_pe(AzureIoTSecurity_IPv6Addresses_t *p, const uint32_t v0[4], const uint32_t v1[4])
{ flatbuffers_uint32_array_copy_from_pe(p->local_address, v0, 4); flatbuffers_uint32_array_copy_from_pe(p->remote_address, v1, 4);
  return p; }
static inline AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_copy_from_pe(AzureIoTSecurity_IPv6Addresses_t *p, const AzureIoTSecurity_IPv6Addresses_t *p2)
{ flatbuffers_uint32_array_copy_from_pe(p->local_address, p2->local_address, 4); flatbuffers_uint32_array_copy_from_pe(p->remote_address, p2->remote_address, 4);
  return p; }
__flatbuffers_build_struct(flatbuffers_, AzureIoTSecurity_IPv6Addresses, 32, 4, AzureIoTSecurity_IPv6Addresses_file_identifier, AzureIoTSecurity_IPv6Addresses_type_identifier)
__flatbuffers_define_fixed_array_primitives(flatbuffers_, AzureIoTSecurity_IPv6Addresses, AzureIoTSecurity_IPv6Addresses_t)

static const flatbuffers_voffset_t __AzureIoTSecurity_NetworkActivityCommon_required[] = { 0 };
typedef flatbuffers_ref_t AzureIoTSecurity_NetworkActivityCommon_ref_t;
static AzureIoTSecurity_NetworkActivityCommon_ref_t AzureIoTSecurity_NetworkActivityCommon_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivityCommon_table_t t);
__flatbuffers_build_table(flatbuffers_, AzureIoTSecurity_NetworkActivityCommon, 9)

static const flatbuffers_voffset_t __AzureIoTSecurity_NetworkActivityV4_required[] = { 0, 1, 0 };
typedef flatbuffers_ref_t AzureIoTSecurity_NetworkActivityV4_ref_t;
static AzureIoTSecurity_NetworkActivityV4_ref_t AzureIoTSecurity_NetworkActivityV4_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivityV4_table_t t);
__flatbuffers_build_table(flatbuffers_, AzureIoTSecurity_NetworkActivityV4, 2)

static const flatbuffers_voffset_t __AzureIoTSecurity_NetworkActivityV6_required[] = { 0, 1, 0 };
typedef flatbuffers_ref_t AzureIoTSecurity_NetworkActivityV6_ref_t;
static AzureIoTSecurity_NetworkActivityV6_ref_t AzureIoTSecurity_NetworkActivityV6_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivityV6_table_t t);
__flatbuffers_build_table(flatbuffers_, AzureIoTSecurity_NetworkActivityV6, 2)

static const flatbuffers_voffset_t __AzureIoTSecurity_NetworkActivity_required[] = { 0 };
typedef flatbuffers_ref_t AzureIoTSecurity_NetworkActivity_ref_t;
static AzureIoTSecurity_NetworkActivity_ref_t AzureIoTSecurity_NetworkActivity_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivity_table_t t);
__flatbuffers_build_table(flatbuffers_, AzureIoTSecurity_NetworkActivity, 2)

#define __AzureIoTSecurity_NetworkActivityCommon_formal_args ,\
  uint16_t v0, uint16_t v1, uint32_t v2, uint32_t v3,\
  AzureIoTSecurity_Protocol_enum_t v4, uint16_t v5, uint16_t v6, flatbuffers_string_ref_t v7, flatbuffers_string_ref_t v8
#define __AzureIoTSecurity_NetworkActivityCommon_call_args ,\
  v0, v1, v2, v3,\
  v4, v5, v6, v7, v8
static inline AzureIoTSecurity_NetworkActivityCommon_ref_t AzureIoTSecurity_NetworkActivityCommon_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivityCommon_formal_args);
__flatbuffers_build_table_prolog(flatbuffers_, AzureIoTSecurity_NetworkActivityCommon, AzureIoTSecurity_NetworkActivityCommon_file_identifier, AzureIoTSecurity_NetworkActivityCommon_type_identifier)

#define __AzureIoTSecurity_NetworkActivityV4_formal_args , AzureIoTSecurity_IPv4Addresses_t *v0, AzureIoTSecurity_NetworkActivityCommon_ref_t v1
#define __AzureIoTSecurity_NetworkActivityV4_call_args , v0, v1
static inline AzureIoTSecurity_NetworkActivityV4_ref_t AzureIoTSecurity_NetworkActivityV4_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivityV4_formal_args);
__flatbuffers_build_table_prolog(flatbuffers_, AzureIoTSecurity_NetworkActivityV4, AzureIoTSecurity_NetworkActivityV4_file_identifier, AzureIoTSecurity_NetworkActivityV4_type_identifier)

#define __AzureIoTSecurity_NetworkActivityV6_formal_args , AzureIoTSecurity_IPv6Addresses_t *v0, AzureIoTSecurity_NetworkActivityCommon_ref_t v1
#define __AzureIoTSecurity_NetworkActivityV6_call_args , v0, v1
static inline AzureIoTSecurity_NetworkActivityV6_ref_t AzureIoTSecurity_NetworkActivityV6_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivityV6_formal_args);
__flatbuffers_build_table_prolog(flatbuffers_, AzureIoTSecurity_NetworkActivityV6, AzureIoTSecurity_NetworkActivityV6_file_identifier, AzureIoTSecurity_NetworkActivityV6_type_identifier)

#define __AzureIoTSecurity_NetworkActivity_formal_args , AzureIoTSecurity_NetworkActivityV4_vec_ref_t v0, AzureIoTSecurity_NetworkActivityV6_vec_ref_t v1
#define __AzureIoTSecurity_NetworkActivity_call_args , v0, v1
static inline AzureIoTSecurity_NetworkActivity_ref_t AzureIoTSecurity_NetworkActivity_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivity_formal_args);
__flatbuffers_build_table_prolog(flatbuffers_, AzureIoTSecurity_NetworkActivity, AzureIoTSecurity_NetworkActivity_file_identifier, AzureIoTSecurity_NetworkActivity_type_identifier)

__flatbuffers_build_scalar_field(0, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_local_port, flatbuffers_uint16, uint16_t, 2, 2, UINT16_C(0), AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_scalar_field(1, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_remote_port, flatbuffers_uint16, uint16_t, 2, 2, UINT16_C(0), AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_scalar_field(2, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_bytes_in, flatbuffers_uint32, uint32_t, 4, 4, UINT32_C(0), AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_scalar_field(3, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_bytes_out, flatbuffers_uint32, uint32_t, 4, 4, UINT32_C(0), AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_scalar_field(4, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_protocol, AzureIoTSecurity_Protocol, AzureIoTSecurity_Protocol_enum_t, 1, 1, INT8_C(0), AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_scalar_field(5, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_process_id, flatbuffers_uint16, uint16_t, 2, 2, UINT16_C(0), AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_scalar_field(6, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_user_id, flatbuffers_uint16, uint16_t, 2, 2, UINT16_C(0), AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_string_field(7, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_executable, AzureIoTSecurity_NetworkActivityCommon)
__flatbuffers_build_string_field(8, flatbuffers_, AzureIoTSecurity_NetworkActivityCommon_commandline, AzureIoTSecurity_NetworkActivityCommon)

static inline AzureIoTSecurity_NetworkActivityCommon_ref_t AzureIoTSecurity_NetworkActivityCommon_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivityCommon_formal_args)
{
    if (AzureIoTSecurity_NetworkActivityCommon_start(B)
        || AzureIoTSecurity_NetworkActivityCommon_bytes_in_add(B, v2)
        || AzureIoTSecurity_NetworkActivityCommon_bytes_out_add(B, v3)
        || AzureIoTSecurity_NetworkActivityCommon_executable_add(B, v7)
        || AzureIoTSecurity_NetworkActivityCommon_commandline_add(B, v8)
        || AzureIoTSecurity_NetworkActivityCommon_local_port_add(B, v0)
        || AzureIoTSecurity_NetworkActivityCommon_remote_port_add(B, v1)
        || AzureIoTSecurity_NetworkActivityCommon_process_id_add(B, v5)
        || AzureIoTSecurity_NetworkActivityCommon_user_id_add(B, v6)
        || AzureIoTSecurity_NetworkActivityCommon_protocol_add(B, v4)) {
        return 0;
    }
    return AzureIoTSecurity_NetworkActivityCommon_end(B);
}

static AzureIoTSecurity_NetworkActivityCommon_ref_t AzureIoTSecurity_NetworkActivityCommon_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivityCommon_table_t t)
{
    __flatbuffers_memoize_begin(B, t);
    if (AzureIoTSecurity_NetworkActivityCommon_start(B)
        || AzureIoTSecurity_NetworkActivityCommon_bytes_in_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_bytes_out_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_executable_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_commandline_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_local_port_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_remote_port_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_process_id_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_user_id_pick(B, t)
        || AzureIoTSecurity_NetworkActivityCommon_protocol_pick(B, t)) {
        return 0;
    }
    __flatbuffers_memoize_end(B, t, AzureIoTSecurity_NetworkActivityCommon_end(B));
}

__flatbuffers_build_struct_field(0, flatbuffers_, AzureIoTSecurity_NetworkActivityV4_addresses, AzureIoTSecurity_IPv4Addresses, 8, 4, AzureIoTSecurity_NetworkActivityV4)
__flatbuffers_build_table_field(1, flatbuffers_, AzureIoTSecurity_NetworkActivityV4_common, AzureIoTSecurity_NetworkActivityCommon, AzureIoTSecurity_NetworkActivityV4)

static inline AzureIoTSecurity_NetworkActivityV4_ref_t AzureIoTSecurity_NetworkActivityV4_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivityV4_formal_args)
{
    if (AzureIoTSecurity_NetworkActivityV4_start(B)
        || AzureIoTSecurity_NetworkActivityV4_addresses_add(B, v0)
        || AzureIoTSecurity_NetworkActivityV4_common_add(B, v1)) {
        return 0;
    }
    return AzureIoTSecurity_NetworkActivityV4_end(B);
}

static AzureIoTSecurity_NetworkActivityV4_ref_t AzureIoTSecurity_NetworkActivityV4_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivityV4_table_t t)
{
    __flatbuffers_memoize_begin(B, t);
    if (AzureIoTSecurity_NetworkActivityV4_start(B)
        || AzureIoTSecurity_NetworkActivityV4_addresses_pick(B, t)
        || AzureIoTSecurity_NetworkActivityV4_common_pick(B, t)) {
        return 0;
    }
    __flatbuffers_memoize_end(B, t, AzureIoTSecurity_NetworkActivityV4_end(B));
}

__flatbuffers_build_struct_field(0, flatbuffers_, AzureIoTSecurity_NetworkActivityV6_addresses, AzureIoTSecurity_IPv6Addresses, 32, 4, AzureIoTSecurity_NetworkActivityV6)
__flatbuffers_build_table_field(1, flatbuffers_, AzureIoTSecurity_NetworkActivityV6_common, AzureIoTSecurity_NetworkActivityCommon, AzureIoTSecurity_NetworkActivityV6)

static inline AzureIoTSecurity_NetworkActivityV6_ref_t AzureIoTSecurity_NetworkActivityV6_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivityV6_formal_args)
{
    if (AzureIoTSecurity_NetworkActivityV6_start(B)
        || AzureIoTSecurity_NetworkActivityV6_addresses_add(B, v0)
        || AzureIoTSecurity_NetworkActivityV6_common_add(B, v1)) {
        return 0;
    }
    return AzureIoTSecurity_NetworkActivityV6_end(B);
}

static AzureIoTSecurity_NetworkActivityV6_ref_t AzureIoTSecurity_NetworkActivityV6_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivityV6_table_t t)
{
    __flatbuffers_memoize_begin(B, t);
    if (AzureIoTSecurity_NetworkActivityV6_start(B)
        || AzureIoTSecurity_NetworkActivityV6_addresses_pick(B, t)
        || AzureIoTSecurity_NetworkActivityV6_common_pick(B, t)) {
        return 0;
    }
    __flatbuffers_memoize_end(B, t, AzureIoTSecurity_NetworkActivityV6_end(B));
}

__flatbuffers_build_table_vector_field(0, flatbuffers_, AzureIoTSecurity_NetworkActivity_ipv4_activity, AzureIoTSecurity_NetworkActivityV4, AzureIoTSecurity_NetworkActivity)
__flatbuffers_build_table_vector_field(1, flatbuffers_, AzureIoTSecurity_NetworkActivity_ipv6_activity, AzureIoTSecurity_NetworkActivityV6, AzureIoTSecurity_NetworkActivity)

static inline AzureIoTSecurity_NetworkActivity_ref_t AzureIoTSecurity_NetworkActivity_create(flatbuffers_builder_t *B __AzureIoTSecurity_NetworkActivity_formal_args)
{
    if (AzureIoTSecurity_NetworkActivity_start(B)
        || AzureIoTSecurity_NetworkActivity_ipv4_activity_add(B, v0)
        || AzureIoTSecurity_NetworkActivity_ipv6_activity_add(B, v1)) {
        return 0;
    }
    return AzureIoTSecurity_NetworkActivity_end(B);
}

static AzureIoTSecurity_NetworkActivity_ref_t AzureIoTSecurity_NetworkActivity_clone(flatbuffers_builder_t *B, AzureIoTSecurity_NetworkActivity_table_t t)
{
    __flatbuffers_memoize_begin(B, t);
    if (AzureIoTSecurity_NetworkActivity_start(B)
        || AzureIoTSecurity_NetworkActivity_ipv4_activity_pick(B, t)
        || AzureIoTSecurity_NetworkActivity_ipv6_activity_pick(B, t)) {
        return 0;
    }
    __flatbuffers_memoize_end(B, t, AzureIoTSecurity_NetworkActivity_end(B));
}

#include "flatcc/flatcc_epilogue.h"
#endif /* NETWORK_ACTIVITY_BUILDER_H */
