#ifndef PROCESS_EVENT_TYPE_BUILDER_H
#define PROCESS_EVENT_TYPE_BUILDER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef PROCESS_EVENT_TYPE_READER_H
#include "process_event_type_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

#define __AzureIoTSecurity_ProcessEventType_formal_args , AzureIoTSecurity_ProcessEventType_enum_t v0
#define __AzureIoTSecurity_ProcessEventType_call_args , v0
__flatbuffers_build_scalar(flatbuffers_, AzureIoTSecurity_ProcessEventType, AzureIoTSecurity_ProcessEventType_enum_t)

#include "flatcc/flatcc_epilogue.h"
#endif /* PROCESS_EVENT_TYPE_BUILDER_H */
