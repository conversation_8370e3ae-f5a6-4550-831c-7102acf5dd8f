#ifndef HEARTBEAT_READER_H
#define HEARTBEAT_READER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif


typedef const struct AzureIoTSecurity_Heartbeat_table *AzureIoTSecurity_Heartbeat_table_t;
typedef struct AzureIoTSecurity_Heartbeat_table *AzureIoTSecurity_Heartbeat_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_Heartbeat_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_Heartbeat_mutable_vec_t;
#ifndef AzureIoTSecurity_Heartbeat_file_identifier
#define AzureIoTSecurity_Heartbeat_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_Heartbeat_file_identifier */
#ifndef AzureIoTSecurity_Heartbeat_identifier
#define AzureIoTSecurity_Heartbeat_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_Heartbeat_type_hash ((flatbuffers_thash_t)0x5e80dfe0)
#define AzureIoTSecurity_Heartbeat_type_identifier "\xe0\xdf\x80\x5e"



struct AzureIoTSecurity_Heartbeat_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_Heartbeat_vec_len(AzureIoTSecurity_Heartbeat_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_Heartbeat_table_t AzureIoTSecurity_Heartbeat_vec_at(AzureIoTSecurity_Heartbeat_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_Heartbeat_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_Heartbeat)



#include "flatcc/flatcc_epilogue.h"
#endif /* HEARTBEAT_READER_H */
