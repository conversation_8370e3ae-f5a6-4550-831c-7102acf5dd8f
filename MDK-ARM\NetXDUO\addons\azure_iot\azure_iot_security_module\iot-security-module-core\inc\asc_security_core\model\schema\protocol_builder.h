#ifndef PROTOCOL_BUILDER_H
#define PROTOCOL_BUILDER_H

/* Generated by flatcc 0.6.1-dev <PERSON>uffers schema compiler for C by dvide.com */

#ifndef PROTOCOL_READER_H
#include "protocol_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

#define __AzureIoTSecurity_Protocol_formal_args , AzureIoTSecurity_Protocol_enum_t v0
#define __AzureIoTSecurity_Protocol_call_args , v0
__flatbuffers_build_scalar(flatbuffers_, AzureIoTSecurity_Protocol, AzureIoTSecurity_Protocol_enum_t)

#include "flatcc/flatcc_epilogue.h"
#endif /* PROTOCOL_BUILDER_H */
