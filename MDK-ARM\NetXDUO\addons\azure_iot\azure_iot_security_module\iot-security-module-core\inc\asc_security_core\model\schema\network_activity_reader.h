#ifndef NETWORK_ACTIVITY_READER_H
#define NETWORK_ACTIVITY_READER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#ifndef PROTOCOL_READER_H
#include "protocol_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

typedef struct AzureIoTSecurity_IPv4Addresses AzureIoTSecurity_IPv4Addresses_t;
typedef const AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_struct_t;
typedef AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_mutable_struct_t;
typedef const AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_vec_t;
typedef AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses_mutable_vec_t;
typedef struct AzureIoTSecurity_IPv6Addresses AzureIoTSecurity_IPv6Addresses_t;
typedef const AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_struct_t;
typedef AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_mutable_struct_t;
typedef const AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_vec_t;
typedef AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses_mutable_vec_t;

typedef const struct AzureIoTSecurity_NetworkActivityCommon_table *AzureIoTSecurity_NetworkActivityCommon_table_t;
typedef struct AzureIoTSecurity_NetworkActivityCommon_table *AzureIoTSecurity_NetworkActivityCommon_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivityCommon_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivityCommon_mutable_vec_t;
typedef const struct AzureIoTSecurity_NetworkActivityV4_table *AzureIoTSecurity_NetworkActivityV4_table_t;
typedef struct AzureIoTSecurity_NetworkActivityV4_table *AzureIoTSecurity_NetworkActivityV4_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivityV4_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivityV4_mutable_vec_t;
typedef const struct AzureIoTSecurity_NetworkActivityV6_table *AzureIoTSecurity_NetworkActivityV6_table_t;
typedef struct AzureIoTSecurity_NetworkActivityV6_table *AzureIoTSecurity_NetworkActivityV6_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivityV6_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivityV6_mutable_vec_t;
typedef const struct AzureIoTSecurity_NetworkActivity_table *AzureIoTSecurity_NetworkActivity_table_t;
typedef struct AzureIoTSecurity_NetworkActivity_table *AzureIoTSecurity_NetworkActivity_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivity_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_NetworkActivity_mutable_vec_t;
#ifndef AzureIoTSecurity_IPv4Addresses_file_identifier
#define AzureIoTSecurity_IPv4Addresses_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_IPv4Addresses_file_identifier */
#ifndef AzureIoTSecurity_IPv4Addresses_identifier
#define AzureIoTSecurity_IPv4Addresses_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_IPv4Addresses_type_hash ((flatbuffers_thash_t)0x1244c83d)
#define AzureIoTSecurity_IPv4Addresses_type_identifier "\x3d\xc8\x44\x12"
#ifndef AzureIoTSecurity_IPv6Addresses_file_identifier
#define AzureIoTSecurity_IPv6Addresses_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_IPv6Addresses_file_identifier */
#ifndef AzureIoTSecurity_IPv6Addresses_identifier
#define AzureIoTSecurity_IPv6Addresses_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_IPv6Addresses_type_hash ((flatbuffers_thash_t)0x2d30aedf)
#define AzureIoTSecurity_IPv6Addresses_type_identifier "\xdf\xae\x30\x2d"
#ifndef AzureIoTSecurity_NetworkActivityCommon_file_identifier
#define AzureIoTSecurity_NetworkActivityCommon_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_NetworkActivityCommon_file_identifier */
#ifndef AzureIoTSecurity_NetworkActivityCommon_identifier
#define AzureIoTSecurity_NetworkActivityCommon_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_NetworkActivityCommon_type_hash ((flatbuffers_thash_t)0xad0bdd1a)
#define AzureIoTSecurity_NetworkActivityCommon_type_identifier "\x1a\xdd\x0b\xad"
#ifndef AzureIoTSecurity_NetworkActivityV4_file_identifier
#define AzureIoTSecurity_NetworkActivityV4_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_NetworkActivityV4_file_identifier */
#ifndef AzureIoTSecurity_NetworkActivityV4_identifier
#define AzureIoTSecurity_NetworkActivityV4_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_NetworkActivityV4_type_hash ((flatbuffers_thash_t)0x5c6a48e9)
#define AzureIoTSecurity_NetworkActivityV4_type_identifier "\xe9\x48\x6a\x5c"
#ifndef AzureIoTSecurity_NetworkActivityV6_file_identifier
#define AzureIoTSecurity_NetworkActivityV6_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_NetworkActivityV6_file_identifier */
#ifndef AzureIoTSecurity_NetworkActivityV6_identifier
#define AzureIoTSecurity_NetworkActivityV6_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_NetworkActivityV6_type_hash ((flatbuffers_thash_t)0x5a6a45c3)
#define AzureIoTSecurity_NetworkActivityV6_type_identifier "\xc3\x45\x6a\x5a"
#ifndef AzureIoTSecurity_NetworkActivity_file_identifier
#define AzureIoTSecurity_NetworkActivity_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_NetworkActivity_file_identifier */
#ifndef AzureIoTSecurity_NetworkActivity_identifier
#define AzureIoTSecurity_NetworkActivity_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_NetworkActivity_type_hash ((flatbuffers_thash_t)0xe433e6cb)
#define AzureIoTSecurity_NetworkActivity_type_identifier "\xcb\xe6\x33\xe4"


struct AzureIoTSecurity_IPv4Addresses {
    /**  Local IPv4 Address (network byte order) */
    alignas(4) uint32_t local_address;
    /**  Remote IPv4 Address (network byte order) */
    alignas(4) uint32_t remote_address;
};
static_assert(sizeof(AzureIoTSecurity_IPv4Addresses_t) == 8, "struct size mismatch");

static inline const AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses__const_ptr_add(const AzureIoTSecurity_IPv4Addresses_t *p, size_t i) { return p + i; }
static inline AzureIoTSecurity_IPv4Addresses_t *AzureIoTSecurity_IPv4Addresses__ptr_add(AzureIoTSecurity_IPv4Addresses_t *p, size_t i) { return p + i; }
static inline AzureIoTSecurity_IPv4Addresses_struct_t AzureIoTSecurity_IPv4Addresses_vec_at(AzureIoTSecurity_IPv4Addresses_vec_t vec, size_t i)
__flatbuffers_struct_vec_at(vec, i)
static inline size_t AzureIoTSecurity_IPv4Addresses__size(void) { return 8; }
static inline size_t AzureIoTSecurity_IPv4Addresses_vec_len(AzureIoTSecurity_IPv4Addresses_vec_t vec)
__flatbuffers_vec_len(vec)
__flatbuffers_struct_as_root(AzureIoTSecurity_IPv4Addresses)

__flatbuffers_define_struct_scalar_field(AzureIoTSecurity_IPv4Addresses, local_address, flatbuffers_uint32, uint32_t)
__flatbuffers_define_struct_scalar_field(AzureIoTSecurity_IPv4Addresses, remote_address, flatbuffers_uint32, uint32_t)

struct AzureIoTSecurity_IPv6Addresses {
    /**  Local IPv6 Address (network byte order) */
    alignas(4) uint32_t local_address[4];
    /**  Remote IPv6 Address (network byte order) */
    alignas(4) uint32_t remote_address[4];
};
static_assert(sizeof(AzureIoTSecurity_IPv6Addresses_t) == 32, "struct size mismatch");

static inline const AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses__const_ptr_add(const AzureIoTSecurity_IPv6Addresses_t *p, size_t i) { return p + i; }
static inline AzureIoTSecurity_IPv6Addresses_t *AzureIoTSecurity_IPv6Addresses__ptr_add(AzureIoTSecurity_IPv6Addresses_t *p, size_t i) { return p + i; }
static inline AzureIoTSecurity_IPv6Addresses_struct_t AzureIoTSecurity_IPv6Addresses_vec_at(AzureIoTSecurity_IPv6Addresses_vec_t vec, size_t i)
__flatbuffers_struct_vec_at(vec, i)
static inline size_t AzureIoTSecurity_IPv6Addresses__size(void) { return 32; }
static inline size_t AzureIoTSecurity_IPv6Addresses_vec_len(AzureIoTSecurity_IPv6Addresses_vec_t vec)
__flatbuffers_vec_len(vec)
__flatbuffers_struct_as_root(AzureIoTSecurity_IPv6Addresses)

__flatbuffers_define_struct_scalar_fixed_array_field(AzureIoTSecurity_IPv6Addresses, local_address, flatbuffers_uint32, uint32_t, 4)
__flatbuffers_define_struct_scalar_fixed_array_field(AzureIoTSecurity_IPv6Addresses, remote_address, flatbuffers_uint32, uint32_t, 4)


struct AzureIoTSecurity_NetworkActivityCommon_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_NetworkActivityCommon_vec_len(AzureIoTSecurity_NetworkActivityCommon_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_NetworkActivityCommon_table_t AzureIoTSecurity_NetworkActivityCommon_vec_at(AzureIoTSecurity_NetworkActivityCommon_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_NetworkActivityCommon_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_NetworkActivityCommon)

/**  Local Port */
__flatbuffers_define_scalar_field(0, AzureIoTSecurity_NetworkActivityCommon, local_port, flatbuffers_uint16, uint16_t, UINT16_C(0))
/**  Remote Port */
__flatbuffers_define_scalar_field(1, AzureIoTSecurity_NetworkActivityCommon, remote_port, flatbuffers_uint16, uint16_t, UINT16_C(0))
/**  Bytes In - Amount of data sent into the device (in bytes) */
__flatbuffers_define_scalar_field(2, AzureIoTSecurity_NetworkActivityCommon, bytes_in, flatbuffers_uint32, uint32_t, UINT32_C(0))
/**  Bytes Out - Amount of data sent from the device (in bytes) */
__flatbuffers_define_scalar_field(3, AzureIoTSecurity_NetworkActivityCommon, bytes_out, flatbuffers_uint32, uint32_t, UINT32_C(0))
/**  Protocol (TCP/UDP/ICMP) */
__flatbuffers_define_scalar_field(4, AzureIoTSecurity_NetworkActivityCommon, protocol, AzureIoTSecurity_Protocol, AzureIoTSecurity_Protocol_enum_t, INT8_C(0))
/**  Process ID - ID of the owner process */
__flatbuffers_define_scalar_field(5, AzureIoTSecurity_NetworkActivityCommon, process_id, flatbuffers_uint16, uint16_t, UINT16_C(0))
/**  User ID - ID of the owner user */
__flatbuffers_define_scalar_field(6, AzureIoTSecurity_NetworkActivityCommon, user_id, flatbuffers_uint16, uint16_t, UINT16_C(0))
/**  Executable - Full path of the running executable */
__flatbuffers_define_string_field(7, AzureIoTSecurity_NetworkActivityCommon, executable, 0)
/**  Command Line - The command used to run the executable */
__flatbuffers_define_string_field(8, AzureIoTSecurity_NetworkActivityCommon, commandline, 0)

struct AzureIoTSecurity_NetworkActivityV4_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_NetworkActivityV4_vec_len(AzureIoTSecurity_NetworkActivityV4_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_NetworkActivityV4_table_t AzureIoTSecurity_NetworkActivityV4_vec_at(AzureIoTSecurity_NetworkActivityV4_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_NetworkActivityV4_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_NetworkActivityV4)

__flatbuffers_define_struct_field(0, AzureIoTSecurity_NetworkActivityV4, addresses, AzureIoTSecurity_IPv4Addresses_struct_t, 1)
__flatbuffers_define_table_field(1, AzureIoTSecurity_NetworkActivityV4, common, AzureIoTSecurity_NetworkActivityCommon_table_t, 1)

struct AzureIoTSecurity_NetworkActivityV6_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_NetworkActivityV6_vec_len(AzureIoTSecurity_NetworkActivityV6_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_NetworkActivityV6_table_t AzureIoTSecurity_NetworkActivityV6_vec_at(AzureIoTSecurity_NetworkActivityV6_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_NetworkActivityV6_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_NetworkActivityV6)

__flatbuffers_define_struct_field(0, AzureIoTSecurity_NetworkActivityV6, addresses, AzureIoTSecurity_IPv6Addresses_struct_t, 1)
__flatbuffers_define_table_field(1, AzureIoTSecurity_NetworkActivityV6, common, AzureIoTSecurity_NetworkActivityCommon_table_t, 1)

struct AzureIoTSecurity_NetworkActivity_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_NetworkActivity_vec_len(AzureIoTSecurity_NetworkActivity_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_NetworkActivity_table_t AzureIoTSecurity_NetworkActivity_vec_at(AzureIoTSecurity_NetworkActivity_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_NetworkActivity_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_NetworkActivity)

/**  IPv4 Activity - Network activity over IPv4 */
__flatbuffers_define_vector_field(0, AzureIoTSecurity_NetworkActivity, ipv4_activity, AzureIoTSecurity_NetworkActivityV4_vec_t, 0)
/**  IPv6 Activity - Network activity over IPv6 */
__flatbuffers_define_vector_field(1, AzureIoTSecurity_NetworkActivity, ipv6_activity, AzureIoTSecurity_NetworkActivityV6_vec_t, 0)


#include "flatcc/flatcc_epilogue.h"
#endif /* NETWORK_ACTIVITY_READER_H */
