#ifndef SYSTEM_INFORMATION_JSON_PRINTER_H
#define SYSTEM_INFORMATION_JSON_PRINTER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_printer.h"
#include "flatcc/flatcc_prologue.h"

static void AzureIoTSecurity_SystemInformation_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td);

static void AzureIoTSecurity_SystemInformation_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td)
{
    flatcc_json_printer_string_field(ctx, td, 0, "os_info", 7);
    flatcc_json_printer_string_field(ctx, td, 1, "kernel_info", 11);
    flatcc_json_printer_string_field(ctx, td, 2, "hw_info", 7);
}

static inline int AzureIoTSecurity_SystemInformation_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_table_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_SystemInformation_print_json_table);
}

#include "flatcc/flatcc_epilogue.h"
#endif /* SYSTEM_INFORMATION_JSON_PRINTER_H */
