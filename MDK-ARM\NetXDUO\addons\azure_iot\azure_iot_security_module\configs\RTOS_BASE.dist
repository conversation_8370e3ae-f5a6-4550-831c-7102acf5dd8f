# set default configuration from specific parent dist
CONF_DEFINE_BASE(${g_core_config_path} base_dist.cmake)

# DIST: overwrite configuration for specific distribution
set(SECURITY_MODULE_VERSION_MAJOR 3)
set(SECURITY_MODULE_VERSION_MINOR 5)
set(SECURITY_MODULE_VERSION_PATCH 2)

set(ASC_COMPONENT_CONFIGURATION OFF)

set(ASC_COLLECTOR_HEARTBEAT_ENABLED ON)
set(ASC_COLLECTOR_SYSTEM_INFORMATION_ENABLED ON)
set(ASC_COLLECTOR_NETWORK_ACTIVITY_ENABLED ON)

set(ASC_COMPONENT_SECURITY_MODULE ON)

set(ASC_COMPONENT_CORE_SUPPORTS_RESTART OFF)
set(ASC_NOTIFIERS_OBJECT_POOL_ENTRIES 2)
set(ASC_LOG_TIMESTAMP_DEFAULT OFF)

set(ASC_BEST_EFFORT_EVENT_LOOP ON)
set(ASC_SECURITY_MODULE_SEND_MESSAGE_RETRY_TIME 3)
set(ASC_SECURITY_MODULE_PENDING_TIME 60*5)

set(ASC_COLLECTOR_NETWORK_ACTIVITY_CAPTURE_UNICAST_ONLY ON)
set(ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV4_OBJECTS_IN_CACHE 64)
set(ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV6_OBJECTS_IN_CACHE 64)

if(ASC_COMPONENT_CORE_SUPPORTS_RESTART)
set(ASC_BE_TIMERS_OBJECT_POOL_ENTRIES 3)
else()
set(ASC_BE_TIMERS_OBJECT_POOL_ENTRIES 2)
endif()
set(FLATCC_NO_ASSERT OFF)
set(FLATCC_ASSERT NX_ASSERT)