#ifndef EVENT_BUILDER_H
#define EVENT_BUILDER_H

/* Generated by flatcc 0.6.1-dev <PERSON>Buffers schema compiler for C by dvide.com */

#ifndef EVENT_READER_H
#include "event_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#ifndef PAYLOAD_BUILDER_H
#include "payload_builder.h"
#endif
#ifndef UUID_BUILDER_H
#include "uuid_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

static const flatbuffers_voffset_t __AzureIoTSecurity_Event_required[] = { 0, 4, 0 };
typedef flatbuffers_ref_t AzureIoTSecurity_Event_ref_t;
static AzureIoTSecurity_Event_ref_t AzureIoTSecurity_Event_clone(flatbuffers_builder_t *B, AzureIoTSecurity_Event_table_t t);
__flatbuffers_build_table(flatbuffers_, AzureIoTSecurity_Event, 5)

#define __AzureIoTSecurity_Event_formal_args , AzureIoTSecurity_UUID_t *v0, uint32_t v1, uint32_t v2, AzureIoTSecurity_Payload_union_ref_t v4
#define __AzureIoTSecurity_Event_call_args , v0, v1, v2, v4
static inline AzureIoTSecurity_Event_ref_t AzureIoTSecurity_Event_create(flatbuffers_builder_t *B __AzureIoTSecurity_Event_formal_args);
__flatbuffers_build_table_prolog(flatbuffers_, AzureIoTSecurity_Event, AzureIoTSecurity_Event_file_identifier, AzureIoTSecurity_Event_type_identifier)

__flatbuffers_build_struct_field(0, flatbuffers_, AzureIoTSecurity_Event_id, AzureIoTSecurity_UUID, 16, 1, AzureIoTSecurity_Event)
__flatbuffers_build_scalar_field(1, flatbuffers_, AzureIoTSecurity_Event_time, flatbuffers_uint32, uint32_t, 4, 4, UINT32_C(0), AzureIoTSecurity_Event)
__flatbuffers_build_scalar_field(2, flatbuffers_, AzureIoTSecurity_Event_collection_interval, flatbuffers_uint32, uint32_t, 4, 4, UINT32_C(0), AzureIoTSecurity_Event)
__flatbuffers_build_union_field(4, flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, AzureIoTSecurity_Event)
__flatbuffers_build_union_table_value_field(flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, NetworkActivity, AzureIoTSecurity_NetworkActivity)
__flatbuffers_build_union_table_value_field(flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, SystemInformation, AzureIoTSecurity_SystemInformation)
__flatbuffers_build_union_table_value_field(flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, ListeningPorts, AzureIoTSecurity_ListeningPorts)
__flatbuffers_build_union_table_value_field(flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, Heartbeat, AzureIoTSecurity_Heartbeat)
__flatbuffers_build_union_table_value_field(flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, Baseline, AzureIoTSecurity_Baseline)
__flatbuffers_build_union_table_value_field(flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, Process, AzureIoTSecurity_Process)
__flatbuffers_build_union_table_value_field(flatbuffers_, AzureIoTSecurity_Event_payload, AzureIoTSecurity_Payload, Log, AzureIoTSecurity_Log)

static inline AzureIoTSecurity_Event_ref_t AzureIoTSecurity_Event_create(flatbuffers_builder_t *B __AzureIoTSecurity_Event_formal_args)
{
    if (AzureIoTSecurity_Event_start(B)
        || AzureIoTSecurity_Event_time_add(B, v1)
        || AzureIoTSecurity_Event_collection_interval_add(B, v2)
        || AzureIoTSecurity_Event_payload_add_value(B, v4)
        || AzureIoTSecurity_Event_id_add(B, v0)
        || AzureIoTSecurity_Event_payload_add_type(B, v4.type)) {
        return 0;
    }
    return AzureIoTSecurity_Event_end(B);
}

static AzureIoTSecurity_Event_ref_t AzureIoTSecurity_Event_clone(flatbuffers_builder_t *B, AzureIoTSecurity_Event_table_t t)
{
    __flatbuffers_memoize_begin(B, t);
    if (AzureIoTSecurity_Event_start(B)
        || AzureIoTSecurity_Event_time_pick(B, t)
        || AzureIoTSecurity_Event_collection_interval_pick(B, t)
        || AzureIoTSecurity_Event_payload_pick(B, t)
        || AzureIoTSecurity_Event_id_pick(B, t)) {
        return 0;
    }
    __flatbuffers_memoize_end(B, t, AzureIoTSecurity_Event_end(B));
}

#include "flatcc/flatcc_epilogue.h"
#endif /* EVENT_BUILDER_H */
