#ifndef MESSAGE_BUILDER_H
#define MESSAGE_BUILDER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef MESSAGE_READER_H
#include "message_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#ifndef EVENT_BUILDER_H
#include "event_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

static const flatbuffers_voffset_t __AzureIoTSecurity_Message_required[] = { 0, 3, 0 };
typedef flatbuffers_ref_t AzureIoTSecurity_Message_ref_t;
static AzureIoTSecurity_Message_ref_t AzureIoTSecurity_Message_clone(flatbuffers_builder_t *B, AzureIoTSecurity_Message_table_t t);
__flatbuffers_build_table(flatbuffers_, AzureIoTSecurity_Message, 4)

#define __AzureIoTSecurity_Message_formal_args , flatbuffers_string_ref_t v0, uint32_t v1, int8_t v2, AzureIoTSecurity_Event_vec_ref_t v3
#define __AzureIoTSecurity_Message_call_args , v0, v1, v2, v3
static inline AzureIoTSecurity_Message_ref_t AzureIoTSecurity_Message_create(flatbuffers_builder_t *B __AzureIoTSecurity_Message_formal_args);
__flatbuffers_build_table_prolog(flatbuffers_, AzureIoTSecurity_Message, AzureIoTSecurity_Message_file_identifier, AzureIoTSecurity_Message_type_identifier)

__flatbuffers_build_string_field(0, flatbuffers_, AzureIoTSecurity_Message_security_module_id, AzureIoTSecurity_Message)
__flatbuffers_build_scalar_field(1, flatbuffers_, AzureIoTSecurity_Message_security_module_version, flatbuffers_uint32, uint32_t, 4, 4, UINT32_C(0), AzureIoTSecurity_Message)
__flatbuffers_build_scalar_field(2, flatbuffers_, AzureIoTSecurity_Message_timezone, flatbuffers_int8, int8_t, 1, 1, INT8_C(0), AzureIoTSecurity_Message)
__flatbuffers_build_table_vector_field(3, flatbuffers_, AzureIoTSecurity_Message_events, AzureIoTSecurity_Event, AzureIoTSecurity_Message)

static inline AzureIoTSecurity_Message_ref_t AzureIoTSecurity_Message_create(flatbuffers_builder_t *B __AzureIoTSecurity_Message_formal_args)
{
    if (AzureIoTSecurity_Message_start(B)
        || AzureIoTSecurity_Message_security_module_id_add(B, v0)
        || AzureIoTSecurity_Message_security_module_version_add(B, v1)
        || AzureIoTSecurity_Message_events_add(B, v3)
        || AzureIoTSecurity_Message_timezone_add(B, v2)) {
        return 0;
    }
    return AzureIoTSecurity_Message_end(B);
}

static AzureIoTSecurity_Message_ref_t AzureIoTSecurity_Message_clone(flatbuffers_builder_t *B, AzureIoTSecurity_Message_table_t t)
{
    __flatbuffers_memoize_begin(B, t);
    if (AzureIoTSecurity_Message_start(B)
        || AzureIoTSecurity_Message_security_module_id_pick(B, t)
        || AzureIoTSecurity_Message_security_module_version_pick(B, t)
        || AzureIoTSecurity_Message_events_pick(B, t)
        || AzureIoTSecurity_Message_timezone_pick(B, t)) {
        return 0;
    }
    __flatbuffers_memoize_end(B, t, AzureIoTSecurity_Message_end(B));
}

#include "flatcc/flatcc_epilogue.h"
#endif /* MESSAGE_BUILDER_H */
