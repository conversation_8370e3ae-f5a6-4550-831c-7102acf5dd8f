/********************
* Core configuration
*********************/
/* Based on 2021.09.28-3.5.1 core tag */

/* ID and version */
#cmakedefine ASC_SECURITY_MODULE_ID "@ASC_SECURITY_MODULE_ID@"
#cmakedefine SECURITY_MODULE_VERSION_MAJOR @SECURITY_MODULE_VERSION_MAJOR@
#cmakedefine SECURITY_MODULE_VERSION_MINOR @SECURITY_MODULE_VERSION_MINOR@
#cmakedefine SECURITY_MODULE_VERSION_PATCH @SECURITY_MODULE_VERSION_PATCH@
#ifndef SECURITY_MODULE_VERSION_MAJOR
#define SECURITY_MODULE_VERSION_MAJOR 0
#endif
#ifndef SECURITY_MODULE_VERSION_MINOR
#define SECURITY_MODULE_VERSION_MINOR 0
#endif
#ifndef SECURITY_MODULE_VERSION_PATCH
#define SECURITY_MODULE_VERSION_PATCH 0
#endif

/* Collectors definitions */
#cmakedefine ASC_DYNAMIC_COLLECTORS_MAX @ASC_DYNAMIC_COLLECTORS_MAX@
#ifndef ASC_DYNAMIC_COLLECTORS_MAX
#define ASC_DYNAMIC_COLLECTORS_MAX 0
#endif
#cmakedefine ASC_COLLECTOR_HEARTBEAT_ENABLED

#cmakedefine ASC_COLLECTOR_BASELINE_ENABLED
#cmakedefine ASC_BASELINE_REPORT_POOL_ENTRIES @ASC_BASELINE_REPORT_POOL_ENTRIES@

#cmakedefine ASC_COLLECTOR_LOG_ENABLED
#cmakedefine ASC_LOG_REPORT_POOL_ENTRIES @ASC_LOG_REPORT_POOL_ENTRIES@

#cmakedefine ASC_COLLECTOR_NETWORK_ACTIVITY_ENABLED
#cmakedefine ASC_COLLECTOR_NETWORK_ACTIVITY_SEND_EMPTY_EVENTS
#cmakedefine ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV4_OBJECTS_IN_CACHE @ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV4_OBJECTS_IN_CACHE@
#cmakedefine ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV6_OBJECTS_IN_CACHE @ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV6_OBJECTS_IN_CACHE@
#ifndef ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV4_OBJECTS_IN_CACHE
#define ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV4_OBJECTS_IN_CACHE 0
#endif
#ifndef ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV6_OBJECTS_IN_CACHE
#define ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV6_OBJECTS_IN_CACHE 0
#endif

#cmakedefine ASC_COLLECTOR_PROCESS_ENABLED
#cmakedefine ASC_COLLECTOR_PROCESS_SEND_EMPTY_EVENTS
#cmakedefine ASC_COLLECTOR_PROCESS_IN_CACHE @ASC_COLLECTOR_PROCESS_IN_CACHE@
#ifndef ASC_COLLECTOR_PROCESS_IN_CACHE
#define ASC_COLLECTOR_PROCESS_IN_CACHE 0
#endif

#cmakedefine ASC_COLLECTOR_SYSTEM_INFORMATION_ENABLED

#cmakedefine ASC_COLLECTOR_LISTENING_PORTS_ENABLED

/* Components definitions */
#cmakedefine ASC_DYNAMIC_FACTORY_ENABLED
#cmakedefine ASC_DYNAMIC_FACTORY_PATH "@ASC_DYNAMIC_FACTORY_PATH@"
#cmakedefine ASC_DYNAMIC_FACTORY_PATH_RUNTIME_SET
#cmakedefine ASC_DYNAMIC_COMPONENTS_MAX @ASC_DYNAMIC_COMPONENTS_MAX@
#ifndef ASC_DYNAMIC_COMPONENTS_MAX
#define ASC_DYNAMIC_COMPONENTS_MAX 0
#endif

#cmakedefine ASC_COMPONENT_COMMAND_EXECUTOR
#cmakedefine ASC_OPERATIONS_POOL_ENTRIES @ASC_OPERATIONS_POOL_ENTRIES@

#cmakedefine ASC_COMPONENT_CONFIGURATION
#cmakedefine ASC_COMPONENT_CONFIGURATION_PLAT

#cmakedefine ASC_COMPONENT_SECURITY_MODULE

/* Collection definitions */
#cmakedefine ASC_FIRST_COLLECTION_INTERVAL @ASC_FIRST_COLLECTION_INTERVAL@
#cmakedefine ASC_HIGH_PRIORITY_INTERVAL @ASC_HIGH_PRIORITY_INTERVAL@
#cmakedefine ASC_MEDIUM_PRIORITY_INTERVAL @ASC_MEDIUM_PRIORITY_INTERVAL@
#cmakedefine ASC_LOW_PRIORITY_INTERVAL @ASC_LOW_PRIORITY_INTERVAL@

/* Dynamic/Static memory */
#cmakedefine ASC_DYNAMIC_MEMORY_ENABLED

/* ROM reduce */
#cmakedefine ASC_COMPONENT_CORE_SUPPORTS_RESTART
#cmakedefine ASC_COLLECTORS_INFO_SUPPORT
#cmakedefine ASC_LINKED_LIST_NODE_SUPPORT

/* Log */
// Highest compiled log level
#cmakedefine ASC_LOG_LEVEL @ASC_LOG_LEVEL@
#cmakedefine ASC_TIME_H_SUPPORT
#cmakedefine ASC_LOG_TIMESTAMP_DEFAULT

/* Notifier definitions */
#cmakedefine ASC_NOTIFIERS_OBJECT_POOL_ENTRIES @ASC_NOTIFIERS_OBJECT_POOL_ENTRIES@

/* Event loop best effort */
#cmakedefine ASC_BEST_EFFORT_EVENT_LOOP
#cmakedefine ASC_BE_TIMERS_OBJECT_POOL_ENTRIES @ASC_BE_TIMERS_OBJECT_POOL_ENTRIES@

/* Flat buffer serializer */
#cmakedefine ASC_SERIALIZER_USE_CUSTOM_ALLOCATOR
#cmakedefine ASC_FLATCC_JSON_PRINTER_OVERWRITE
#cmakedefine ASC_EMITTER_PAGE_CACHE_SIZE @ASC_EMITTER_PAGE_CACHE_SIZE@
#cmakedefine FLATCC_NO_ASSERT
/* FLATCC_ASSERT might be redefined in platform area */
#cmakedefine FLATCC_ASSERT @FLATCC_ASSERT@
#cmakedefine FLATCC_USE_GENERIC_ALIGNED_ALLOC
#cmakedefine FLATCC_EMITTER_PAGE_SIZE @FLATCC_EMITTER_PAGE_SIZE@

/* Tests definitions */
// Set ASC_FIRST_FORCE_COLLECTION_INTERVAL to '-1' to force immediatly collecting
#cmakedefine ASC_FIRST_FORCE_COLLECTION_INTERVAL @ASC_FIRST_FORCE_COLLECTION_INTERVAL@
#cmakedefine ASC_EXTRA_BE_TIMERS_OBJECT_POOL_ENTRIES @ASC_EXTRA_BE_TIMERS_OBJECT_POOL_ENTRIES@
#cmakedefine ASC_EXTRA_NOTIFIERS_OBJECT_POOL_ENTRIES @ASC_EXTRA_NOTIFIERS_OBJECT_POOL_ENTRIES@
#cmakedefine ASC_EXTRA_COMPONENTS_COUNT @ASC_EXTRA_COMPONENTS_COUNT@
#ifndef ASC_EXTRA_COMPONENTS_COUNT
#define ASC_EXTRA_COMPONENTS_COUNT 0
#endif
#cmakedefine ASC_EXTRA_COLLECTORS_COUNT @ASC_EXTRA_COLLECTORS_COUNT@
#ifndef ASC_EXTRA_COLLECTORS_COUNT
#define ASC_EXTRA_COLLECTORS_COUNT 0
#endif

#define LOOP_ASSERT_FAIL for (\;\;) { }
#define LOOP_ASSERT(s) if (!(s)) {LOOP_ASSERT_FAIL}
