#ifndef FLATBUFFERS_COMMON_BUILDER_H
#define FLATBUFFERS_COMMON_BUILDER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

/* Common FlatBuffers build functionality for C. */

#include "flatcc/flatcc_prologue.h"
#ifndef FLATBUILDER_H
#include "flatcc/flatcc_builder.h"
#endif
typedef flatcc_builder_t flatbuffers_builder_t;
typedef flatcc_builder_ref_t flatbuffers_ref_t;
typedef flatcc_builder_ref_t flatbuffers_vec_ref_t;
typedef flatcc_builder_union_ref_t flatbuffers_union_ref_t;
typedef flatcc_builder_union_vec_ref_t flatbuffers_union_vec_ref_t;
/* integer return code (ref and ptr always fail on 0) */
#define flatbuffers_failed(x) ((x) < 0)
typedef flatbuffers_ref_t flatbuffers_root_t;
#define flatbuffers_root(ref) ((flatbuffers_root_t)(ref))

#define __flatbuffers_memoize_begin(B, src)\
do { flatcc_builder_ref_t _ref; if ((_ref = flatcc_builder_refmap_find((B), (src)))) return _ref; } while (0)
#define __flatbuffers_memoize_end(B, src, op) do { return flatcc_builder_refmap_insert((B), (src), (op)); } while (0)
#define __flatbuffers_memoize(B, src, op) do { __flatbuffers_memoize_begin(B, src); __flatbuffers_memoize_end(B, src, op); } while (0)

#define __flatbuffers_build_buffer(NS)\
typedef NS ## ref_t NS ## buffer_ref_t;\
static inline int NS ## buffer_start(NS ## builder_t *B, const NS ##fid_t fid)\
{ return flatcc_builder_start_buffer(B, fid, 0, 0); }\
static inline int NS ## buffer_start_with_size(NS ## builder_t *B, const NS ##fid_t fid)\
{ return flatcc_builder_start_buffer(B, fid, 0, flatcc_builder_with_size); }\
static inline int NS ## buffer_start_aligned(NS ## builder_t *B, NS ##fid_t fid, uint16_t block_align)\
{ return flatcc_builder_start_buffer(B, fid, block_align, 0); }\
static inline int NS ## buffer_start_aligned_with_size(NS ## builder_t *B, NS ##fid_t fid, uint16_t block_align)\
{ return flatcc_builder_start_buffer(B, fid, block_align, flatcc_builder_with_size); }\
static inline NS ## buffer_ref_t NS ## buffer_end(NS ## builder_t *B, NS ## ref_t root)\
{ return flatcc_builder_end_buffer(B, root); }

#define __flatbuffers_build_table_root(NS, N, FID, TFID)\
static inline int N ## _start_as_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, FID) ? -1 : N ## _start(B); }\
static inline int N ## _start_as_root_with_size(NS ## builder_t *B)\
{ return NS ## buffer_start_with_size(B, FID) ? -1 : N ## _start(B); }\
static inline int N ## _start_as_typed_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, TFID) ? -1 : N ## _start(B); }\
static inline int N ## _start_as_typed_root_with_size(NS ## builder_t *B)\
{ return NS ## buffer_start_with_size(B, TFID) ? -1 : N ## _start(B); }\
static inline NS ## buffer_ref_t N ## _end_as_root(NS ## builder_t *B)\
{ return NS ## buffer_end(B, N ## _end(B)); }\
static inline NS ## buffer_ref_t N ## _end_as_typed_root(NS ## builder_t *B)\
{ return NS ## buffer_end(B, N ## _end(B)); }\
static inline NS ## buffer_ref_t N ## _create_as_root(NS ## builder_t *B __ ## N ## _formal_args)\
{ if (NS ## buffer_start(B, FID)) return 0; return NS ## buffer_end(B, N ## _create(B __ ## N ## _call_args)); }\
static inline NS ## buffer_ref_t N ## _create_as_root_with_size(NS ## builder_t *B __ ## N ## _formal_args)\
{ if (NS ## buffer_start_with_size(B, FID)) return 0; return NS ## buffer_end(B, N ## _create(B __ ## N ## _call_args)); }\
static inline NS ## buffer_ref_t N ## _create_as_typed_root(NS ## builder_t *B __ ## N ## _formal_args)\
{ if (NS ## buffer_start(B, TFID)) return 0; return NS ## buffer_end(B, N ## _create(B __ ## N ## _call_args)); }\
static inline NS ## buffer_ref_t N ## _create_as_typed_root_with_size(NS ## builder_t *B __ ## N ## _formal_args)\
{ if (NS ## buffer_start_with_size(B, TFID)) return 0; return NS ## buffer_end(B, N ## _create(B __ ## N ## _call_args)); }\
static inline NS ## buffer_ref_t N ## _clone_as_root(NS ## builder_t *B, N ## _table_t t)\
{ if (NS ## buffer_start(B, FID)) return 0; return NS ## buffer_end(B, N ## _clone(B, t)); }\
static inline NS ## buffer_ref_t N ## _clone_as_root_with_size(NS ## builder_t *B, N ## _table_t t)\
{ if (NS ## buffer_start_with_size(B, FID)) return 0; return NS ## buffer_end(B, N ## _clone(B, t)); }\
static inline NS ## buffer_ref_t N ## _clone_as_typed_root(NS ## builder_t *B, N ## _table_t t)\
{ if (NS ## buffer_start(B, TFID)) return 0;return NS ## buffer_end(B, N ## _clone(B, t)); }\
static inline NS ## buffer_ref_t N ## _clone_as_typed_root_with_size(NS ## builder_t *B, N ## _table_t t)\
{ if (NS ## buffer_start_with_size(B, TFID)) return 0; return NS ## buffer_end(B, N ## _clone(B, t)); }

#define __flatbuffers_build_table_prolog(NS, N, FID, TFID)\
__flatbuffers_build_table_vector_ops(NS, N ## _vec, N)\
__flatbuffers_build_table_root(NS, N, FID, TFID)

#define __flatbuffers_build_struct_root(NS, N, A, FID, TFID)\
static inline N ## _t *N ## _start_as_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, FID) ? 0 : N ## _start(B); }\
static inline N ## _t *N ## _start_as_root_with_size(NS ## builder_t *B)\
{ return NS ## buffer_start_with_size(B, FID) ? 0 : N ## _start(B); }\
static inline N ## _t *N ## _start_as_typed_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, TFID) ? 0 : N ## _start(B); }\
static inline N ## _t *N ## _start_as_typed_root_with_size(NS ## builder_t *B)\
{ return NS ## buffer_start_with_size(B, TFID) ? 0 : N ## _start(B); }\
static inline NS ## buffer_ref_t N ## _end_as_root(NS ## builder_t *B)\
{ return NS ## buffer_end(B, N ## _end(B)); }\
static inline NS ## buffer_ref_t N ## _end_as_typed_root(NS ## builder_t *B)\
{ return NS ## buffer_end(B, N ## _end(B)); }\
static inline NS ## buffer_ref_t N ## _end_pe_as_root(NS ## builder_t *B)\
{ return NS ## buffer_end(B, N ## _end_pe(B)); }\
static inline NS ## buffer_ref_t N ## _end_pe_as_typed_root(NS ## builder_t *B)\
{ return NS ## buffer_end(B, N ## _end_pe(B)); }\
static inline NS ## buffer_ref_t N ## _create_as_root(NS ## builder_t *B __ ## N ## _formal_args)\
{ return flatcc_builder_create_buffer(B, FID, 0,\
  N ## _create(B __ ## N ## _call_args), A, 0); }\
static inline NS ## buffer_ref_t N ## _create_as_root_with_size(NS ## builder_t *B __ ## N ## _formal_args)\
{ return flatcc_builder_create_buffer(B, FID, 0,\
  N ## _create(B __ ## N ## _call_args), A, flatcc_builder_with_size); }\
static inline NS ## buffer_ref_t N ## _create_as_typed_root(NS ## builder_t *B __ ## N ## _formal_args)\
{ return flatcc_builder_create_buffer(B, TFID, 0,\
  N ## _create(B __ ## N ## _call_args), A, 0); }\
static inline NS ## buffer_ref_t N ## _create_as_typed_root_with_size(NS ## builder_t *B __ ## N ## _formal_args)\
{ return flatcc_builder_create_buffer(B, TFID, 0,\
  N ## _create(B __ ## N ## _call_args), A, flatcc_builder_with_size); }\
static inline NS ## buffer_ref_t N ## _clone_as_root(NS ## builder_t *B, N ## _struct_t p)\
{ return flatcc_builder_create_buffer(B, FID, 0, N ## _clone(B, p), A, 0); }\
static inline NS ## buffer_ref_t N ## _clone_as_root_with_size(NS ## builder_t *B, N ## _struct_t p)\
{ return flatcc_builder_create_buffer(B, FID, 0, N ## _clone(B, p), A, flatcc_builder_with_size); }\
static inline NS ## buffer_ref_t N ## _clone_as_typed_root(NS ## builder_t *B, N ## _struct_t p)\
{ return flatcc_builder_create_buffer(B, TFID, 0, N ## _clone(B, p), A, 0); }\
static inline NS ## buffer_ref_t N ## _clone_as_typed_root_with_size(NS ## builder_t *B, N ## _struct_t p)\
{ return flatcc_builder_create_buffer(B, TFID, 0, N ## _clone(B, p), A, flatcc_builder_with_size); }

#define __flatbuffers_build_nested_table_root(NS, N, TN, FID, TFID)\
static inline int N ## _start_as_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, FID) ? -1 : TN ## _start(B); }\
static inline int N ## _start_as_typed_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, TFID) ? -1 : TN ## _start(B); }\
static inline int N ## _end_as_root(NS ## builder_t *B)\
{ return N ## _add(B, NS ## buffer_end(B, TN ## _end(B))); }\
static inline int N ## _end_as_typed_root(NS ## builder_t *B)\
{ return N ## _add(B, NS ## buffer_end(B, TN ## _end(B))); }\
static inline int N ## _nest(NS ## builder_t *B, void *data, size_t size, uint16_t align)\
{ return N ## _add(B, flatcc_builder_create_vector(B, data, size, 1,\
  align ? align : 8, FLATBUFFERS_COUNT_MAX(1))); }\
static inline int N ## _typed_nest(NS ## builder_t *B, void *data, size_t size, uint16_t align)\
{ return N ## _add(B, flatcc_builder_create_vector(B, data, size, 1,\
  align ? align : 8, FLATBUFFERS_COUNT_MAX(1))); }\
static inline int N ## _clone_as_root(NS ## builder_t *B, TN ## _table_t t)\
{ return N ## _add(B, TN ## _clone_as_root(B, t)); }\
static inline int N ## _clone_as_typed_root(NS ## builder_t *B, TN ## _table_t t)\
{ return N ## _add(B, TN ## _clone_as_typed_root(B, t)); }

#define __flatbuffers_build_nested_struct_root(NS, N, TN, A, FID, TFID)\
static inline TN ## _t *N ## _start_as_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, FID) ? 0 : TN ## _start(B); }\
static inline TN ## _t *N ## _start_as_typed_root(NS ## builder_t *B)\
{ return NS ## buffer_start(B, FID) ? 0 : TN ## _start(B); }\
static inline int N ## _end_as_root(NS ## builder_t *B)\
{ return N ## _add(B, NS ## buffer_end(B, TN ## _end(B))); }\
static inline int N ## _end_as_typed_root(NS ## builder_t *B)\
{ return N ## _add(B, NS ## buffer_end(B, TN ## _end(B))); }\
static inline int N ## _end_pe_as_root(NS ## builder_t *B)\
{ return N ## _add(B, NS ## buffer_end(B, TN ## _end_pe(B))); }\
static inline int N ## _create_as_root(NS ## builder_t *B __ ## TN ## _formal_args)\
{ return N ## _add(B, flatcc_builder_create_buffer(B, FID, 0,\
  TN ## _create(B __ ## TN ## _call_args), A, flatcc_builder_is_nested)); }\
static inline int N ## _create_as_typed_root(NS ## builder_t *B __ ## TN ## _formal_args)\
{ return N ## _add(B, flatcc_builder_create_buffer(B, TFID, 0,\
  TN ## _create(B __ ## TN ## _call_args), A, flatcc_builder_is_nested)); }\
static inline int N ## _nest(NS ## builder_t *B, void *data, size_t size, uint16_t align)\
{ return N ## _add(B, flatcc_builder_create_vector(B, data, size, 1,\
  align < A ? A : align, FLATBUFFERS_COUNT_MAX(1))); }\
static inline int N ## _typed_nest(NS ## builder_t *B, void *data, size_t size, uint16_t align)\
{ return N ## _add(B, flatcc_builder_create_vector(B, data, size, 1,\
  align < A ? A : align, FLATBUFFERS_COUNT_MAX(1))); }\
static inline int N ## _clone_as_root(NS ## builder_t *B, TN ## _struct_t p)\
{ return N ## _add(B, TN ## _clone_as_root(B, p)); }\
static inline int N ## _clone_as_typed_root(NS ## builder_t *B, TN ## _struct_t p)\
{ return N ## _add(B, TN ## _clone_as_typed_root(B, p)); }

#define __flatbuffers_build_vector_ops(NS, V, N, TN, T)\
static inline T *V ## _extend(NS ## builder_t *B, size_t len)\
{ return (T *)flatcc_builder_extend_vector(B, len); }\
static inline T *V ## _append(NS ## builder_t *B, const T *data, size_t len)\
{ return (T *)flatcc_builder_append_vector(B, data, len); }\
static inline int V ## _truncate(NS ## builder_t *B, size_t len)\
{ return flatcc_builder_truncate_vector(B, len); }\
static inline T *V ## _edit(NS ## builder_t *B)\
{ return (T *)flatcc_builder_vector_edit(B); }\
static inline size_t V ## _reserved_len(NS ## builder_t *B)\
{ return flatcc_builder_vector_count(B); }\
static inline T *V ## _push(NS ## builder_t *B, const T *p)\
{ T *_p; return (_p = (T *)flatcc_builder_extend_vector(B, 1)) ? (memcpy(_p, p, TN ## __size()), _p) : 0; }\
static inline T *V ## _push_copy(NS ## builder_t *B, const T *p)\
{ T *_p; return (_p = (T *)flatcc_builder_extend_vector(B, 1)) ? TN ## _copy(_p, p) : 0; }\
static inline T *V ## _push_clone(NS ## builder_t *B, const T *p)\
{ T *_p; return (_p = (T *)flatcc_builder_extend_vector(B, 1)) ? TN ## _copy(_p, p) : 0; }\
static inline T *V ## _push_create(NS ## builder_t *B __ ## TN ## _formal_args)\
{ T *_p; return (_p = (T *)flatcc_builder_extend_vector(B, 1)) ? TN ## _assign(_p __ ## TN ## _call_args) : 0; }

#define __flatbuffers_build_vector(NS, N, T, S, A)\
typedef NS ## ref_t N ## _vec_ref_t;\
static inline int N ## _vec_start(NS ## builder_t *B)\
{ return flatcc_builder_start_vector(B, S, A, FLATBUFFERS_COUNT_MAX(S)); }\
static inline N ## _vec_ref_t N ## _vec_end_pe(NS ## builder_t *B)\
{ return flatcc_builder_end_vector(B); }\
static inline N ## _vec_ref_t N ## _vec_end(NS ## builder_t *B)\
{ if (!NS ## is_native_pe()) { size_t i, n; T *p = (T *)flatcc_builder_vector_edit(B);\
    for (i = 0, n = flatcc_builder_vector_count(B); i < n; ++i)\
    { N ## _to_pe(N ## __ptr_add(p, i)); }} return flatcc_builder_end_vector(B); }\
static inline N ## _vec_ref_t N ## _vec_create_pe(NS ## builder_t *B, const T *data, size_t len)\
{ return flatcc_builder_create_vector(B, data, len, S, A, FLATBUFFERS_COUNT_MAX(S)); }\
static inline N ## _vec_ref_t N ## _vec_create(NS ## builder_t *B, const T *data, size_t len)\
{ if (!NS ## is_native_pe()) { size_t i; T *p; int ret = flatcc_builder_start_vector(B, S, A, FLATBUFFERS_COUNT_MAX(S)); if (ret) { return ret; }\
  p = (T *)flatcc_builder_extend_vector(B, len); if (!p) return 0;\
  for (i = 0; i < len; ++i) { N ## _copy_to_pe(N ## __ptr_add(p, i), N ## __const_ptr_add(data, i)); }\
  return flatcc_builder_end_vector(B); } else return flatcc_builder_create_vector(B, data, len, S, A, FLATBUFFERS_COUNT_MAX(S)); }\
static inline N ## _vec_ref_t N ## _vec_clone(NS ## builder_t *B, N ##_vec_t vec)\
{ __flatbuffers_memoize(B, vec, flatcc_builder_create_vector(B, vec, N ## _vec_len(vec), S, A, FLATBUFFERS_COUNT_MAX(S))); }\
static inline N ## _vec_ref_t N ## _vec_slice(NS ## builder_t *B, N ##_vec_t vec, size_t index, size_t len)\
{ size_t n = N ## _vec_len(vec); if (index >= n) index = n; n -= index; if (len > n) len = n;\
  return flatcc_builder_create_vector(B, N ## __const_ptr_add(vec, index), len, S, A, FLATBUFFERS_COUNT_MAX(S)); }\
__flatbuffers_build_vector_ops(NS, N ## _vec, N, N, T)

#define __flatbuffers_build_union_vector_ops(NS, V, N, TN)\
static inline TN ## _union_ref_t *V ## _extend(NS ## builder_t *B, size_t len)\
{ return flatcc_builder_extend_union_vector(B, len); }\
static inline TN ## _union_ref_t *V ## _append(NS ## builder_t *B, const TN ## _union_ref_t *data, size_t len)\
{ return flatcc_builder_append_union_vector(B, data, len); }\
static inline int V ## _truncate(NS ## builder_t *B, size_t len)\
{ return flatcc_builder_truncate_union_vector(B, len); }\
static inline TN ## _union_ref_t *V ## _edit(NS ## builder_t *B)\
{ return (TN ## _union_ref_t *) flatcc_builder_union_vector_edit(B); }\
static inline size_t V ## _reserved_len(NS ## builder_t *B)\
{ return flatcc_builder_union_vector_count(B); }\
static inline TN ## _union_ref_t *V ## _push(NS ## builder_t *B, const TN ## _union_ref_t ref)\
{ return flatcc_builder_union_vector_push(B, ref); }\
static inline TN ## _union_ref_t *V ## _push_clone(NS ## builder_t *B, TN ## _union_t u)\
{ return TN ## _vec_push(B, TN ## _clone(B, u)); }

#define __flatbuffers_build_union_vector(NS, N)\
static inline int N ## _vec_start(NS ## builder_t *B)\
{ return flatcc_builder_start_union_vector(B); }\
static inline N ## _union_vec_ref_t N ## _vec_end(NS ## builder_t *B)\
{ return flatcc_builder_end_union_vector(B); }\
static inline N ## _union_vec_ref_t N ## _vec_create(NS ## builder_t *B, const N ## _union_ref_t *data, size_t len)\
{ return flatcc_builder_create_union_vector(B, data, len); }\
__flatbuffers_build_union_vector_ops(NS, N ## _vec, N, N)\
/* Preserves DAG structure separately for type and value vector, so a type vector could be shared for many value vectors. */\
static inline N ## _union_vec_ref_t N ## _vec_clone(NS ## builder_t *B, N ##_union_vec_t vec)\
{ N ## _union_vec_ref_t _uvref, _ret = { 0, 0 }; NS ## union_ref_t _uref; size_t _i, _len;\
  if (vec.type == 0) return _ret;\
  _uvref.type = flatcc_builder_refmap_find(B, vec.type); _uvref.value = flatcc_builder_refmap_find(B, vec.value);\
  _len = N ## _union_vec_len(vec); if (_uvref.type == 0) {\
  _uvref.type = flatcc_builder_refmap_insert(B, vec.type, (flatcc_builder_create_type_vector(B, vec.type, _len))); }\
  if (_uvref.type == 0) return _ret; if (_uvref.value == 0) {\
  if (flatcc_builder_start_offset_vector(B)) return _ret;\
  for (_i = 0; _i < _len; ++_i) { _uref = N ## _clone(B, N ## _union_vec_at(vec, _i));\
    if (!_uref.value || !(flatcc_builder_offset_vector_push(B, _uref.value))) return _ret; }\
  _uvref.value = flatcc_builder_refmap_insert(B, vec.value, flatcc_builder_end_offset_vector(B));\
  if (_uvref.value == 0) return _ret; } return _uvref; }

#define __flatbuffers_build_string_vector_ops(NS, N)\
static inline int N ## _push_start(NS ## builder_t *B)\
{ return NS ## string_start(B); }\
static inline NS ## string_ref_t *N ## _push_end(NS ## builder_t *B)\
{ return NS ## string_vec_push(B, NS ## string_end(B)); }\
static inline NS ## string_ref_t *N ## _push_create(NS ## builder_t *B, const char *s, size_t len)\
{ return NS ## string_vec_push(B, NS ## string_create(B, s, len)); }\
static inline NS ## string_ref_t *N ## _push_create_str(NS ## builder_t *B, const char *s)\
{ return NS ## string_vec_push(B, NS ## string_create_str(B, s)); }\
static inline NS ## string_ref_t *N ## _push_create_strn(NS ## builder_t *B, const char *s, size_t max_len)\
{ return NS ## string_vec_push(B, NS ## string_create_strn(B, s, max_len)); }\
static inline NS ## string_ref_t *N ## _push_clone(NS ## builder_t *B, NS ## string_t string)\
{ return NS ## string_vec_push(B, NS ## string_clone(B, string)); }\
static inline NS ## string_ref_t *N ## _push_slice(NS ## builder_t *B, NS ## string_t string, size_t index, size_t len)\
{ return NS ## string_vec_push(B, NS ## string_slice(B, string, index, len)); }

#define __flatbuffers_build_table_vector_ops(NS, N, TN)\
static inline int N ## _push_start(NS ## builder_t *B)\
{ return TN ## _start(B); }\
static inline TN ## _ref_t *N ## _push_end(NS ## builder_t *B)\
{ return N ## _push(B, TN ## _end(B)); }\
static inline TN ## _ref_t *N ## _push_create(NS ## builder_t *B __ ## TN ##_formal_args)\
{ return N ## _push(B, TN ## _create(B __ ## TN ## _call_args)); }

#define __flatbuffers_build_offset_vector_ops(NS, V, N, TN)\
static inline TN ## _ref_t *V ## _extend(NS ## builder_t *B, size_t len)\
{ return flatcc_builder_extend_offset_vector(B, len); }\
static inline TN ## _ref_t *V ## _append(NS ## builder_t *B, const TN ## _ref_t *data, size_t len)\
{ return flatcc_builder_append_offset_vector(B, data, len); }\
static inline int V ## _truncate(NS ## builder_t *B, size_t len)\
{ return flatcc_builder_truncate_offset_vector(B, len); }\
static inline TN ## _ref_t *V ## _edit(NS ## builder_t *B)\
{ return (TN ## _ref_t *)flatcc_builder_offset_vector_edit(B); }\
static inline size_t V ## _reserved_len(NS ## builder_t *B)\
{ return flatcc_builder_offset_vector_count(B); }\
static inline TN ## _ref_t *V ## _push(NS ## builder_t *B, const TN ## _ref_t ref)\
{ return ref ? flatcc_builder_offset_vector_push(B, ref) : 0; }

#define __flatbuffers_build_offset_vector(NS, N)\
typedef NS ## ref_t N ## _vec_ref_t;\
static inline int N ## _vec_start(NS ## builder_t *B)\
{ return flatcc_builder_start_offset_vector(B); }\
static inline N ## _vec_ref_t N ## _vec_end(NS ## builder_t *B)\
{ return flatcc_builder_end_offset_vector(B); }\
static inline N ## _vec_ref_t N ## _vec_create(NS ## builder_t *B, const N ## _ref_t *data, size_t len)\
{ return flatcc_builder_create_offset_vector(B, data, len); }\
__flatbuffers_build_offset_vector_ops(NS, N ## _vec, N, N)\
static inline N ## _vec_ref_t N ## _vec_clone(NS ## builder_t *B, N ##_vec_t vec)\
{ N ## _ref_t _e; size_t _i, _len; __flatbuffers_memoize_begin(B, vec);\
 _len = N ## _vec_len(vec); if (flatcc_builder_start_offset_vector(B)) return 0;\
  for (_i = 0; _i < _len; ++_i) { if (!(_e = N ## _clone(B, N ## _vec_at(vec, _i)))) return 0;\
    if (!flatcc_builder_offset_vector_push(B, _e)) return 0; }\
  __flatbuffers_memoize_end(B, vec, flatcc_builder_end_offset_vector(B)); }

#define __flatbuffers_build_string_ops(NS, N)\
static inline char *N ## _append(NS ## builder_t *B, const char *s, size_t len)\
{ return flatcc_builder_append_string(B, s, len); }\
static inline char *N ## _append_str(NS ## builder_t *B, const char *s)\
{ return flatcc_builder_append_string_str(B, s); }\
static inline char *N ## _append_strn(NS ## builder_t *B, const char *s, size_t len)\
{ return flatcc_builder_append_string_strn(B, s, len); }\
static inline size_t N ## _reserved_len(NS ## builder_t *B)\
{ return flatcc_builder_string_len(B); }\
static inline char *N ## _extend(NS ## builder_t *B, size_t len)\
{ return flatcc_builder_extend_string(B, len); }\
static inline char *N ## _edit(NS ## builder_t *B)\
{ return flatcc_builder_string_edit(B); }\
static inline int N ## _truncate(NS ## builder_t *B, size_t len)\
{ return flatcc_builder_truncate_string(B, len); }

#define __flatbuffers_build_string(NS)\
typedef NS ## ref_t NS ## string_ref_t;\
static inline int NS ## string_start(NS ## builder_t *B)\
{ return flatcc_builder_start_string(B); }\
static inline NS ## string_ref_t NS ## string_end(NS ## builder_t *B)\
{ return flatcc_builder_end_string(B); }\
static inline NS ## ref_t NS ## string_create(NS ## builder_t *B, const char *s, size_t len)\
{ return flatcc_builder_create_string(B, s, len); }\
static inline NS ## ref_t NS ## string_create_str(NS ## builder_t *B, const char *s)\
{ return flatcc_builder_create_string_str(B, s); }\
static inline NS ## ref_t NS ## string_create_strn(NS ## builder_t *B, const char *s, size_t len)\
{ return flatcc_builder_create_string_strn(B, s, len); }\
static inline NS ## string_ref_t NS ## string_clone(NS ## builder_t *B, NS ## string_t string)\
{ __flatbuffers_memoize(B, string, flatcc_builder_create_string(B, string, NS ## string_len(string))); }\
static inline NS ## string_ref_t NS ## string_slice(NS ## builder_t *B, NS ## string_t string, size_t index, size_t len)\
{ size_t n = NS ## string_len(string); if (index >= n) index = n; n -= index; if (len > n) len = n;\
  return flatcc_builder_create_string(B, string + index, len); }\
__flatbuffers_build_string_ops(NS, NS ## string)\
__flatbuffers_build_offset_vector(NS, NS ## string)

#define __flatbuffers_copy_from_pe(P, P2, N) (*(P) = N ## _read_from_pe(P2), (P))
#define __flatbuffers_from_pe(P, N) (*(P) = N ## _read_from_pe(P), (P))
#define __flatbuffers_copy_to_pe(P, P2, N) (N ## _write_to_pe((P), *(P2)), (P))
#define __flatbuffers_to_pe(P, N) (N ## _write_to_pe((P), *(P)), (P))
#define __flatbuffers_define_fixed_array_primitives(NS, N, T)\
static inline T *N ## _array_copy(T *p, const T *p2, size_t n)\
{ memcpy(p, p2, n * sizeof(T)); return p; }\
static inline T *N ## _array_copy_from_pe(T *p, const T *p2, size_t n)\
{ size_t i; if (NS ## is_native_pe()) memcpy(p, p2, n * sizeof(T)); else\
  for (i = 0; i < n; ++i) N ## _copy_from_pe(&p[i], &p2[i]); return p; }\
static inline T *N ## _array_copy_to_pe(T *p, const T *p2, size_t n)\
{ size_t i; if (NS ## is_native_pe()) memcpy(p, p2, n * sizeof(T)); else\
  for (i = 0; i < n; ++i) N ## _copy_to_pe(&p[i], &p2[i]); return p; }
#define __flatbuffers_define_scalar_primitives(NS, N, T)\
static inline T *N ## _from_pe(T *p) { return __ ## NS ## from_pe(p, N); }\
static inline T *N ## _to_pe(T *p) { return __ ## NS ## to_pe(p, N); }\
static inline T *N ## _copy(T *p, const T *p2) { *p = *p2; return p; }\
static inline T *N ## _copy_from_pe(T *p, const T *p2)\
{ return __ ## NS ## copy_from_pe(p, p2, N); }\
static inline T *N ## _copy_to_pe(T *p, const T *p2) \
{ return __ ## NS ## copy_to_pe(p, p2, N); }\
static inline T *N ## _assign(T *p, const T v0) { *p = v0; return p; }\
static inline T *N ## _assign_from_pe(T *p, T v0)\
{ *p = N ## _read_from_pe(&v0); return p; }\
static inline T *N ## _assign_to_pe(T *p, T v0)\
{ N ## _write_to_pe(p, v0); return p; }
#define __flatbuffers_build_scalar(NS, N, T)\
__ ## NS ## define_scalar_primitives(NS, N, T)\
__ ## NS ## define_fixed_array_primitives(NS, N, T)\
__ ## NS ## build_vector(NS, N, T, sizeof(T), sizeof(T))
/* Depends on generated copy_to/from_pe functions, and the type. */
#define __flatbuffers_define_struct_primitives(NS, N)\
static inline N ## _t *N ##_to_pe(N ## _t *p)\
{ if (!NS ## is_native_pe()) { N ## _copy_to_pe(p, p); }; return p; }\
static inline N ## _t *N ##_from_pe(N ## _t *p)\
{ if (!NS ## is_native_pe()) { N ## _copy_from_pe(p, p); }; return p; }\
static inline N ## _t *N ## _clear(N ## _t *p) { return (N ## _t *)memset(p, 0, N ## __size()); }

/* Depends on generated copy/assign_to/from_pe functions, and the type. */
#define __flatbuffers_build_struct(NS, N, S, A, FID, TFID)\
__ ## NS ## define_struct_primitives(NS, N)\
typedef NS ## ref_t N ## _ref_t;\
static inline N ## _t *N ## _start(NS ## builder_t *B)\
{ return (N ## _t *)flatcc_builder_start_struct(B, S, A); }\
static inline N ## _ref_t N ## _end(NS ## builder_t *B)\
{ if (!NS ## is_native_pe()) { N ## _to_pe((N ## _t *)flatcc_builder_struct_edit(B)); }\
  return flatcc_builder_end_struct(B); }\
static inline N ## _ref_t N ## _end_pe(NS ## builder_t *B)\
{ return flatcc_builder_end_struct(B); }\
static inline N ## _ref_t N ## _create(NS ## builder_t *B __ ## N ## _formal_args)\
{ N ## _t *_p = N ## _start(B); if (!_p) return 0; N ##_assign_to_pe(_p __ ## N ## _call_args);\
  return N ## _end_pe(B); }\
static inline N ## _ref_t N ## _clone(NS ## builder_t *B, N ## _struct_t p)\
{ N ## _t *_p; __flatbuffers_memoize_begin(B, p); _p = N ## _start(B); if (!_p) return 0;\
  N ## _copy(_p, p); __flatbuffers_memoize_end(B, p, N ##_end_pe(B)); }\
__flatbuffers_build_vector(NS, N, N ## _t, S, A)\
__flatbuffers_build_struct_root(NS, N, A, FID, TFID)\

#define __flatbuffers_struct_clear_field(p) memset((p), 0, sizeof(*(p)))
#define __flatbuffers_build_table(NS, N, K)\
static inline int N ## _start(NS ## builder_t *B)\
{ return flatcc_builder_start_table(B, K); }\
static inline N ## _ref_t N ## _end(NS ## builder_t *B)\
{ FLATCC_ASSERT(flatcc_builder_check_required(B, __ ## N ## _required,\
  sizeof(__ ## N ## _required) / sizeof(__ ## N ## _required[0]) - 1));\
  return flatcc_builder_end_table(B); }\
__flatbuffers_build_offset_vector(NS, N)

#define __flatbuffers_build_table_field(ID, NS, N, TN, TT)\
static inline int N ## _add(NS ## builder_t *B, TN ## _ref_t ref)\
{ TN ## _ref_t *_p; return (ref && (_p = flatcc_builder_table_add_offset(B, ID))) ?\
  ((*_p = ref), 0) : -1; }\
static inline int N ## _start(NS ## builder_t *B)\
{ return TN ## _start(B); }\
static inline int N ## _end(NS ## builder_t *B)\
{ return N ## _add(B, TN ## _end(B)); }\
static inline TN ## _ref_t N ## _create(NS ## builder_t *B __ ## TN ##_formal_args)\
{ return N ## _add(B, TN ## _create(B __ ## TN ## _call_args)); }\
static inline int N ## _clone(NS ## builder_t *B, TN ## _table_t p)\
{ return N ## _add(B, TN ## _clone(B, p)); }\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ TN ## _table_t _p = N ## _get(t); return _p ? N ## _clone(B, _p) : 0; }

#define __flatbuffers_build_union_field(ID, NS, N, TN, TT)\
static inline int N ## _add(NS ## builder_t *B, TN ## _union_ref_t uref)\
{ NS ## ref_t *_p; TN ## _union_type_t *_pt; if (uref.type == TN ## _NONE) return 0; if (uref.value == 0) return -1;\
  if (!(_pt = (TN ## _union_type_t *)flatcc_builder_table_add(B, ID - 1, sizeof(*_pt), sizeof(*_pt)))) return -1;\
  *_pt = uref.type; if (!(_p = flatcc_builder_table_add_offset(B, ID))) return -1; *_p = uref.value; return 0; }\
static inline int N ## _add_type(NS ## builder_t *B, TN ## _union_type_t type)\
{ TN ## _union_type_t *_pt; if (type == TN ## _NONE) return 0; return (_pt = (TN ## _union_type_t *)flatcc_builder_table_add(B, ID - 1,\
  sizeof(*_pt), sizeof(*_pt))) ? ((*_pt = type), 0) : -1; }\
static inline int N ## _add_value(NS ## builder_t *B, TN ## _union_ref_t uref)\
{ NS ## ref_t *p; if (uref.type == TN ## _NONE) return 0; return (p = flatcc_builder_table_add_offset(B, ID)) ?\
  ((*p = uref.value), 0) : -1; }\
static inline int N ## _clone(NS ## builder_t *B, TN ## _union_t p)\
{ return N ## _add(B, TN ## _clone(B, p)); }\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ TN ## _union_t _p = N ## _union(t); return _p.type ? N ## _clone(B, _p) : 0; }

/* M is the union value name and T is its type, i.e. the qualified name. */
#define __flatbuffers_build_union_table_value_field(NS, N, NU, M, T)\
static inline int N ## _ ## M ## _add(NS ## builder_t *B, T ## _ref_t ref)\
{ return N ## _add(B, NU ## _as_ ## M (ref)); }\
static inline int N ## _ ## M ## _start(NS ## builder_t *B)\
{ return T ## _start(B); }\
static inline int N ## _ ## M ## _end(NS ## builder_t *B)\
{ T ## _ref_t ref = T ## _end(B);\
  return ref ? N ## _ ## M ## _add(B, ref) : -1; }\
static inline int N ## _ ## M ## _create(NS ## builder_t *B __ ## T ##_formal_args)\
{ T ## _ref_t ref = T ## _create(B __ ## T ## _call_args);\
  return ref ? N ## _add(B, NU ## _as_ ## M(ref)) : -1; }\
static inline int N ## _ ## M ## _clone(NS ## builder_t *B, T ## _table_t t)\
{ T ## _ref_t ref = T ## _clone(B, t);\
  return ref ? N ## _add(B, NU ## _as_ ## M(ref)) : -1; }

/* M is the union value name and T is its type, i.e. the qualified name. */
#define __flatbuffers_build_union_struct_value_field(NS, N, NU, M, T)\
static inline int N ## _ ## M ## _add(NS ## builder_t *B, T ## _ref_t ref)\
{ return N ## _add(B, NU ## _as_ ## M (ref)); }\
static inline T ## _t *N ## _ ## M ## _start(NS ## builder_t *B)\
{ return T ## _start(B); }\
static inline int N ## _ ## M ## _end(NS ## builder_t *B)\
{ T ## _ref_t ref = T ## _end(B);\
  return ref ? N ## _ ## M ## _add(B, ref) : -1; }\
static inline int N ## _ ## M ## _create(NS ## builder_t *B __ ## T ##_formal_args)\
{ T ## _ref_t ref = T ## _create(B __ ## T ## _call_args);\
  return ref ? N ## _add(B, NU ## _as_ ## M(ref)) : -1; }\
static inline int N ## _ ## M ## _end_pe(NS ## builder_t *B)\
{ T ## _ref_t ref = T ## _end_pe(B);\
  return ref ? N ## _add(B, NU ## _as_ ## M(ref)) : -1; }\
static inline int N ## _ ## M ## _clone(NS ## builder_t *B, T ## _struct_t p)\
{ T ## _ref_t ref = T ## _clone(B, p);\
  return ref ? N ## _add(B, NU ## _as_ ## M(ref)) : -1; }
#define __flatbuffers_build_union_string_value_field(NS, N, NU, M)\
static inline int N ## _ ## M ## _add(NS ## builder_t *B, NS ## string_ref_t ref)\
{ return N ## _add(B, NU ## _as_ ## M (ref)); }\
__flatbuffers_build_string_field_ops(NS, N ## _ ## M)

/* NS: common namespace, ID: table field id (not offset), TN: name of type T, TT: name of table type
 * S: sizeof of scalar type, A: alignment of type T, default value V of type T. */
#define __flatbuffers_build_scalar_field(ID, NS, N, TN, T, S, A, V, TT)\
static inline int N ## _add(NS ## builder_t *B, const T v)\
{ T *_p; if (v == V) return 0; if (!(_p = (T *)flatcc_builder_table_add(B, ID, S, A))) return -1;\
  TN ## _assign_to_pe(_p, v); return 0; }\
static inline int N ## _force_add(NS ## builder_t *B, const T v)\
{ T *_p; if (!(_p = (T *)flatcc_builder_table_add(B, ID, S, A))) return -1;\
  TN ## _assign_to_pe(_p, v); return 0; }\
/* Clone does not skip default values and expects pe endian content. */\
static inline int N ## _clone(NS ## builder_t *B, const T *p)\
{ return 0 == flatcc_builder_table_add_copy(B, ID, p, S, A) ? -1 : 0; }\
/* Transferring a missing field is a nop success with 0 as result. */\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ const T *_p = N ## _get_ptr(t); return _p ? N ## _clone(B, _p) : 0; }

#define __flatbuffers_build_struct_field(ID, NS, N, TN, S, A, TT)\
static inline TN ## _t *N ## _start(NS ## builder_t *B)\
{ return (TN ## _t *)flatcc_builder_table_add(B, ID, S, A); }\
static inline int N ## _end(NS ## builder_t *B)\
{ if (!NS ## is_native_pe()) { TN ## _to_pe((TN ## _t *)flatcc_builder_table_edit(B, S)); } return 0; }\
static inline int N ## _end_pe(NS ## builder_t *B) { return 0; }\
static inline int N ## _create(NS ## builder_t *B __ ## TN ## _formal_args)\
{ TN ## _t *_p = N ## _start(B); if (!_p) return -1; TN ##_assign_to_pe(_p __ ## TN ## _call_args);\
  return 0; }\
static inline int N ## _add(NS ## builder_t *B, const TN ## _t *p)\
{ TN ## _t *_p = N ## _start(B); if (!_p) return -1; TN ##_copy_to_pe(_p, p); return 0; }\
static inline int N ## _clone(NS ## builder_t *B, TN ## _struct_t p)\
{ return 0 == flatcc_builder_table_add_copy(B, ID, p, S, A) ? -1 : 0; }\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ TN ## _struct_t _p = N ## _get(t); return _p ? N ## _clone(B, _p) : 0; }

#define __flatbuffers_build_vector_field(ID, NS, N, TN, T, TT)\
static inline int N ## _add(NS ## builder_t *B, TN ## _vec_ref_t ref)\
{ TN ## _vec_ref_t *_p; return (ref && (_p = flatcc_builder_table_add_offset(B, ID))) ? ((*_p = ref), 0) : -1; }\
static inline int N ## _start(NS ## builder_t *B)\
{ return TN ## _vec_start(B); }\
static inline int N ## _end_pe(NS ## builder_t *B)\
{ return N ## _add(B, TN ## _vec_end_pe(B)); }\
static inline int N ## _end(NS ## builder_t *B)\
{ return N ## _add(B, TN ## _vec_end(B)); }\
static inline int N ## _create_pe(NS ## builder_t *B, const T *data, size_t len)\
{ return N ## _add(B, TN ## _vec_create_pe(B, data, len)); }\
static inline int N ## _create(NS ## builder_t *B, const T *data, size_t len)\
{ return N ## _add(B, TN ## _vec_create(B, data, len)); }\
static inline int N ## _slice(NS ## builder_t *B, TN ## _vec_t vec, size_t index, size_t len)\
{ return N ## _add(B, TN ## _vec_slice(B, vec, index, len)); }\
static inline int N ## _clone(NS ## builder_t *B, TN ## _vec_t vec)\
{ return N ## _add(B, TN ## _vec_clone(B, vec)); }\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ TN ## _vec_t _p = N ## _get(t); return _p ? N ## _clone(B, _p) : 0; }\
__flatbuffers_build_vector_ops(NS, N, N, TN, T)\

#define __flatbuffers_build_offset_vector_field(ID, NS, N, TN, TT)\
static inline int N ## _add(NS ## builder_t *B, TN ## _vec_ref_t ref)\
{ TN ## _vec_ref_t *_p; return (ref && (_p = flatcc_builder_table_add_offset(B, ID))) ? ((*_p = ref), 0) : -1; }\
static inline int N ## _start(NS ## builder_t *B)\
{ return flatcc_builder_start_offset_vector(B); }\
static inline int N ## _end(NS ## builder_t *B)\
{ return N ## _add(B, flatcc_builder_end_offset_vector(B)); }\
static inline int N ## _create(NS ## builder_t *B, const TN ## _ref_t *data, size_t len)\
{ return N ## _add(B, flatcc_builder_create_offset_vector(B, data, len)); }\
__flatbuffers_build_offset_vector_ops(NS, N, N, TN)\
static inline int N ## _clone(NS ## builder_t *B, TN ## _vec_t vec)\
{ return N ## _add(B, TN ## _vec_clone(B, vec)); }\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ TN ## _vec_t _p = N ## _get(t); return _p ? N ## _clone(B, _p) : 0; }

/* depends on N ## _add which differs for union member fields and ordinary fields */\
#define __flatbuffers_build_string_field_ops(NS, N)\
static inline int N ## _start(NS ## builder_t *B)\
{ return flatcc_builder_start_string(B); }\
static inline int N ## _end(NS ## builder_t *B)\
{ return N ## _add(B, flatcc_builder_end_string(B)); }\
static inline int N ## _create(NS ## builder_t *B, const char *s, size_t len)\
{ return N ## _add(B, flatcc_builder_create_string(B, s, len)); }\
static inline int N ## _create_str(NS ## builder_t *B, const char *s)\
{ return N ## _add(B, flatcc_builder_create_string_str(B, s)); }\
static inline int N ## _create_strn(NS ## builder_t *B, const char *s, size_t max_len)\
{ return N ## _add(B, flatcc_builder_create_string_strn(B, s, max_len)); }\
static inline int N ## _clone(NS ## builder_t *B, NS ## string_t string)\
{ return N ## _add(B, NS ## string_clone(B, string)); }\
static inline int N ## _slice(NS ## builder_t *B, NS ## string_t string, size_t index, size_t len)\
{ return N ## _add(B, NS ## string_slice(B, string, index, len)); }\
__flatbuffers_build_string_ops(NS, N)

#define __flatbuffers_build_string_field(ID, NS, N, TT)\
static inline int N ## _add(NS ## builder_t *B, NS ## string_ref_t ref)\
{ NS ## string_ref_t *_p; return (ref && (_p = flatcc_builder_table_add_offset(B, ID))) ? ((*_p = ref), 0) : -1; }\
__flatbuffers_build_string_field_ops(NS, N)\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ NS ## string_t _p = N ## _get(t); return _p ? N ## _clone(B, _p) : 0; }

#define __flatbuffers_build_table_vector_field(ID, NS, N, TN, TT)\
__flatbuffers_build_offset_vector_field(ID, NS, N, TN, TT)\
__flatbuffers_build_table_vector_ops(NS, N, TN)

#define __flatbuffers_build_union_vector_field(ID, NS, N, TN, TT)\
static inline int N ## _add(NS ## builder_t *B, TN ## _union_vec_ref_t uvref)\
{ NS ## vec_ref_t *_p; if (!uvref.type || !uvref.value) return uvref.type == uvref.value ? 0 : -1;\
  if (!(_p = flatcc_builder_table_add_offset(B, ID - 1))) return -1; *_p = uvref.type;\
  if (!(_p = flatcc_builder_table_add_offset(B, ID))) return -1; *_p = uvref.value; return 0; }\
static inline int N ## _start(NS ## builder_t *B)\
{ return flatcc_builder_start_union_vector(B); }\
static inline int N ## _end(NS ## builder_t *B)\
{ return N ## _add(B, flatcc_builder_end_union_vector(B)); }\
static inline int N ## _create(NS ## builder_t *B, const TN ## _union_ref_t *data, size_t len)\
{ return N ## _add(B, flatcc_builder_create_union_vector(B, data, len)); }\
__flatbuffers_build_union_vector_ops(NS, N, N, TN)\
static inline int N ## _clone(NS ## builder_t *B, TN ## _union_vec_t vec)\
{ return N ## _add(B, TN ## _vec_clone(B, vec)); }\
static inline int N ## _pick(NS ## builder_t *B, TT ## _table_t t)\
{ TN ## _union_vec_t _p = N ## _union(t); return _p.type ? N ## _clone(B, _p) : 0; }

#define __flatbuffers_build_union_table_vector_value_field(NS, N, NU, M, T)\
static inline int N ## _ ## M ## _push_start(NS ## builder_t *B)\
{ return T ## _start(B); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_end(NS ## builder_t *B)\
{ return NU ## _vec_push(B, NU ## _as_ ## M (T ## _end(B))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push(NS ## builder_t *B, T ## _ref_t ref)\
{ return NU ## _vec_push(B, NU ## _as_ ## M (ref)); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_create(NS ## builder_t *B __ ## T ##_formal_args)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(T ## _create(B __ ## T ## _call_args))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_clone(NS ## builder_t *B, T ## _table_t t)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(T ## _clone(B, t))); }

#define __flatbuffers_build_union_struct_vector_value_field(NS, N, NU, M, T)\
static inline T ## _t *N ## _ ## M ## _push_start(NS ## builder_t *B)\
{ return T ## _start(B); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_end(NS ## builder_t *B)\
{ return NU ## _vec_push(B, NU ## _as_ ## M (T ## _end(B))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push(NS ## builder_t *B, T ## _ref_t ref)\
{ return NU ## _vec_push(B, NU ## _as_ ## M (ref)); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_create(NS ## builder_t *B __ ## T ##_formal_args)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(T ## _create(B __ ## T ## _call_args))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_clone(NS ## builder_t *B, T ## _struct_t p)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(T ## _clone(B, p))); }

#define __flatbuffers_build_union_string_vector_value_field(NS, N, NU, M)\
static inline NU ## _union_ref_t *N ## _ ## M ## _push(NS ## builder_t *B, NS ## string_ref_t ref)\
{ return NU ## _vec_push(B, NU ## _as_ ## M (ref)); }\
static inline int N ## _ ## M ## _push_start(NS ## builder_t *B)\
{ return NS ## string_start(B); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_end(NS ## builder_t *B)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(NS ## string_end(B))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_create(NS ## builder_t *B, const char *s, size_t len)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(NS ## string_create(B, s, len))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_create_str(NS ## builder_t *B, const char *s)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(NS ## string_create_str(B, s))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_create_strn(NS ## builder_t *B, const char *s, size_t max_len)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(NS ## string_create_strn(B, s, max_len))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_clone(NS ## builder_t *B, NS ## string_t string)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(NS ## string_clone(B, string))); }\
static inline NU ## _union_ref_t *N ## _ ## M ## _push_slice(NS ## builder_t *B, NS ## string_t string, size_t index, size_t len)\
{ return NU ## _vec_push(B, NU ## _as_ ## M(NS ## string_slice(B, string, index, len))); }

#define __flatbuffers_build_string_vector_field(ID, NS, N, TT)\
__flatbuffers_build_offset_vector_field(ID, NS, N, NS ## string, TT)\
__flatbuffers_build_string_vector_ops(NS, N)

#define __flatbuffers_char_formal_args , char v0
#define __flatbuffers_char_call_args , v0
#define __flatbuffers_uint8_formal_args , uint8_t v0
#define __flatbuffers_uint8_call_args , v0
#define __flatbuffers_int8_formal_args , int8_t v0
#define __flatbuffers_int8_call_args , v0
#define __flatbuffers_bool_formal_args , flatbuffers_bool_t v0
#define __flatbuffers_bool_call_args , v0
#define __flatbuffers_uint16_formal_args , uint16_t v0
#define __flatbuffers_uint16_call_args , v0
#define __flatbuffers_uint32_formal_args , uint32_t v0
#define __flatbuffers_uint32_call_args , v0
#define __flatbuffers_uint64_formal_args , uint64_t v0
#define __flatbuffers_uint64_call_args , v0
#define __flatbuffers_int16_formal_args , int16_t v0
#define __flatbuffers_int16_call_args , v0
#define __flatbuffers_int32_formal_args , int32_t v0
#define __flatbuffers_int32_call_args , v0
#define __flatbuffers_int64_formal_args , int64_t v0
#define __flatbuffers_int64_call_args , v0
#define __flatbuffers_float_formal_args , float v0
#define __flatbuffers_float_call_args , v0
#define __flatbuffers_double_formal_args , double v0
#define __flatbuffers_double_call_args , v0

__flatbuffers_build_scalar(flatbuffers_, flatbuffers_char, char)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_uint8, uint8_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_int8, int8_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_bool, flatbuffers_bool_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_uint16, uint16_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_uint32, uint32_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_uint64, uint64_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_int16, int16_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_int32, int32_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_int64, int64_t)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_float, float)
__flatbuffers_build_scalar(flatbuffers_, flatbuffers_double, double)

__flatbuffers_build_string(flatbuffers_)

__flatbuffers_build_buffer(flatbuffers_)
#include "flatcc/flatcc_epilogue.h"
#endif /* FLATBUFFERS_COMMON_BUILDER_H */
