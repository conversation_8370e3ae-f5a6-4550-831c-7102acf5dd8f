#ifndef PROCESS_EVENT_TYPE_READER_H
#define PROCESS_EVENT_TYPE_READER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif



typedef int8_t AzureIoTSecurity_ProcessEventType_enum_t;
__flatbuffers_define_integer_type(AzureIoTSecurity_ProcessEventType, AzureIoTSecurity_ProcessEventType_enum_t, 8)
#define AzureIoTSecurity_ProcessEventType_FORK ((AzureIoTSecurity_ProcessEventType_enum_t)INT8_C(0))
#define AzureIoTSecurity_ProcessEventType_EXEC ((AzureIoTSecurity_ProcessEventType_enum_t)INT8_C(1))
#define AzureIoTSecurity_ProcessEventType_EXIT ((AzureIoTSecurity_ProcessEventType_enum_t)INT8_C(2))

static inline const char *AzureIoTSecurity_ProcessEventType_name(AzureIoTSecurity_ProcessEventType_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_ProcessEventType_FORK: return "FORK";
    case AzureIoTSecurity_ProcessEventType_EXEC: return "EXEC";
    case AzureIoTSecurity_ProcessEventType_EXIT: return "EXIT";
    default: return "";
    }
}

static inline int AzureIoTSecurity_ProcessEventType_is_known_value(AzureIoTSecurity_ProcessEventType_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_ProcessEventType_FORK: return 1;
    case AzureIoTSecurity_ProcessEventType_EXEC: return 1;
    case AzureIoTSecurity_ProcessEventType_EXIT: return 1;
    default: return 0;
    }
}




#include "flatcc/flatcc_epilogue.h"
#endif /* PROCESS_EVENT_TYPE_READER_H */
