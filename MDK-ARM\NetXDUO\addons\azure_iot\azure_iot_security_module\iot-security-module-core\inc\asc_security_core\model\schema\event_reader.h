#ifndef EVENT_READER_H
#define EVENT_READER_H

/* Generated by flatcc 0.6.1-dev <PERSON>uffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#ifndef PAYLOAD_READER_H
#include "payload_reader.h"
#endif
#ifndef UUID_READER_H
#include "uuid_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif


typedef const struct AzureIoTSecurity_Event_table *AzureIoTSecurity_Event_table_t;
typedef struct AzureIoTSecurity_Event_table *AzureIoTSecurity_Event_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_Event_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_Event_mutable_vec_t;
#ifndef AzureIoTSecurity_Event_file_identifier
#define AzureIoTSecurity_Event_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_Event_file_identifier */
#ifndef AzureIoTSecurity_Event_identifier
#define AzureIoTSecurity_Event_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_Event_type_hash ((flatbuffers_thash_t)0xb37ca898)
#define AzureIoTSecurity_Event_type_identifier "\x98\xa8\x7c\xb3"



struct AzureIoTSecurity_Event_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_Event_vec_len(AzureIoTSecurity_Event_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_Event_table_t AzureIoTSecurity_Event_vec_at(AzureIoTSecurity_Event_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_Event_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_Event)

/**  Event ID */
__flatbuffers_define_struct_field(0, AzureIoTSecurity_Event, id, AzureIoTSecurity_UUID_struct_t, 1)
/**  Event Timestamp (seconds since epoch) */
__flatbuffers_define_scalar_field(1, AzureIoTSecurity_Event, time, flatbuffers_uint32, uint32_t, UINT32_C(0))
/**  Event Collection Interval */
__flatbuffers_define_scalar_field(2, AzureIoTSecurity_Event, collection_interval, flatbuffers_uint32, uint32_t, UINT32_C(0))
/**  Event Payload */
__flatbuffers_define_union_field(flatbuffers_, 4, AzureIoTSecurity_Event, payload, AzureIoTSecurity_Payload, 1)


#include "flatcc/flatcc_epilogue.h"
#endif /* EVENT_READER_H */
