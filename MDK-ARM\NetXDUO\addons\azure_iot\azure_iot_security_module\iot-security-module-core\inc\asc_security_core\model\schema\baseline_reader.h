#ifndef BASELINE_READER_H
#define BASELINE_READER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif


typedef const struct AzureIoTSecurity_BaselineCheck_table *AzureIoTSecurity_BaselineCheck_table_t;
typedef struct AzureIoTSecurity_BaselineCheck_table *AzureIoTSecurity_BaselineCheck_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_BaselineCheck_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_BaselineCheck_mutable_vec_t;
typedef const struct AzureIoTSecurity_Baseline_table *AzureIoTSecurity_Baseline_table_t;
typedef struct AzureIoTSecurity_Baseline_table *AzureIoTSecurity_Baseline_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_Baseline_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_Baseline_mutable_vec_t;
#ifndef AzureIoTSecurity_BaselineCheck_file_identifier
#define AzureIoTSecurity_BaselineCheck_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_BaselineCheck_file_identifier */
#ifndef AzureIoTSecurity_BaselineCheck_identifier
#define AzureIoTSecurity_BaselineCheck_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_BaselineCheck_type_hash ((flatbuffers_thash_t)0xc125ef19)
#define AzureIoTSecurity_BaselineCheck_type_identifier "\x19\xef\x25\xc1"
#ifndef AzureIoTSecurity_Baseline_file_identifier
#define AzureIoTSecurity_Baseline_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_Baseline_file_identifier */
#ifndef AzureIoTSecurity_Baseline_identifier
#define AzureIoTSecurity_Baseline_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_Baseline_type_hash ((flatbuffers_thash_t)0xac65eeb7)
#define AzureIoTSecurity_Baseline_type_identifier "\xb7\xee\x65\xac"

typedef int8_t AzureIoTSecurity_Result_enum_t;
__flatbuffers_define_integer_type(AzureIoTSecurity_Result, AzureIoTSecurity_Result_enum_t, 8)
#define AzureIoTSecurity_Result_PASS ((AzureIoTSecurity_Result_enum_t)INT8_C(0))
#define AzureIoTSecurity_Result_FAIL ((AzureIoTSecurity_Result_enum_t)INT8_C(1))
#define AzureIoTSecurity_Result_ERROR ((AzureIoTSecurity_Result_enum_t)INT8_C(2))

static inline const char *AzureIoTSecurity_Result_name(AzureIoTSecurity_Result_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_Result_PASS: return "PASS";
    case AzureIoTSecurity_Result_FAIL: return "FAIL";
    case AzureIoTSecurity_Result_ERROR: return "ERROR";
    default: return "";
    }
}

static inline int AzureIoTSecurity_Result_is_known_value(AzureIoTSecurity_Result_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_Result_PASS: return 1;
    case AzureIoTSecurity_Result_FAIL: return 1;
    case AzureIoTSecurity_Result_ERROR: return 1;
    default: return 0;
    }
}

typedef int8_t AzureIoTSecurity_Severity_enum_t;
__flatbuffers_define_integer_type(AzureIoTSecurity_Severity, AzureIoTSecurity_Severity_enum_t, 8)
#define AzureIoTSecurity_Severity_CRITICAL ((AzureIoTSecurity_Severity_enum_t)INT8_C(0))
#define AzureIoTSecurity_Severity_IMPORTANT ((AzureIoTSecurity_Severity_enum_t)INT8_C(1))
#define AzureIoTSecurity_Severity_WARNING ((AzureIoTSecurity_Severity_enum_t)INT8_C(2))
#define AzureIoTSecurity_Severity_INFORMATIONAL ((AzureIoTSecurity_Severity_enum_t)INT8_C(3))

static inline const char *AzureIoTSecurity_Severity_name(AzureIoTSecurity_Severity_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_Severity_CRITICAL: return "CRITICAL";
    case AzureIoTSecurity_Severity_IMPORTANT: return "IMPORTANT";
    case AzureIoTSecurity_Severity_WARNING: return "WARNING";
    case AzureIoTSecurity_Severity_INFORMATIONAL: return "INFORMATIONAL";
    default: return "";
    }
}

static inline int AzureIoTSecurity_Severity_is_known_value(AzureIoTSecurity_Severity_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_Severity_CRITICAL: return 1;
    case AzureIoTSecurity_Severity_IMPORTANT: return 1;
    case AzureIoTSecurity_Severity_WARNING: return 1;
    case AzureIoTSecurity_Severity_INFORMATIONAL: return 1;
    default: return 0;
    }
}



struct AzureIoTSecurity_BaselineCheck_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_BaselineCheck_vec_len(AzureIoTSecurity_BaselineCheck_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_BaselineCheck_table_t AzureIoTSecurity_BaselineCheck_vec_at(AzureIoTSecurity_BaselineCheck_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_BaselineCheck_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_BaselineCheck)

/**  ID - The ID of the Baseline check */
__flatbuffers_define_string_field(0, AzureIoTSecurity_BaselineCheck, id, 1)
/**  The result of the check, PASS if the check passed, FAIL if the check failed, ERROR if there was an error while trying to run it */
__flatbuffers_define_scalar_field(1, AzureIoTSecurity_BaselineCheck, result, AzureIoTSecurity_Result, AzureIoTSecurity_Result_enum_t, INT8_C(0))
/**  The error message */
__flatbuffers_define_string_field(2, AzureIoTSecurity_BaselineCheck, error, 0)
/**  The decription of the baseline check */
__flatbuffers_define_string_field(3, AzureIoTSecurity_BaselineCheck, description, 0)
/**  The severity of the problem */
__flatbuffers_define_scalar_field(4, AzureIoTSecurity_BaselineCheck, severity, AzureIoTSecurity_Severity, AzureIoTSecurity_Severity_enum_t, INT8_C(0))
/**  The suggested remediation */
__flatbuffers_define_string_field(5, AzureIoTSecurity_BaselineCheck, remediation, 0)

struct AzureIoTSecurity_Baseline_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_Baseline_vec_len(AzureIoTSecurity_Baseline_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_Baseline_table_t AzureIoTSecurity_Baseline_vec_at(AzureIoTSecurity_Baseline_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_Baseline_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_Baseline)

__flatbuffers_define_vector_field(0, AzureIoTSecurity_Baseline, baseline_checks, AzureIoTSecurity_BaselineCheck_vec_t, 0)


#include "flatcc/flatcc_epilogue.h"
#endif /* BASELINE_READER_H */
