#ifndef EVENT_JSON_PARSER_H
#define EVENT_JSON_PARSER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_parser.h"
#ifndef PAYLOAD_JSON_PARSER_H
#include "payload_json_parser.h"
#endif
#ifndef UUID_JSON_PARSER_H
#include "uuid_json_parser.h"
#endif
#include "flatcc/flatcc_prologue.h"

static const char *AzureIoTSecurity_Event_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *event_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *event_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *event_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);

static const char *AzureIoTSecurity_Event_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;
    size_t h_unions;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 5)) goto failed;
    if (end == flatcc_json_parser_prepare_unions(ctx, buf, end, 1, &h_unions)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w < 0x7061796c6f616400) { /* branch "payload" */
            if (w == 0x636f6c6c65637469) { /* descend "collecti" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if (w == 0x6f6e5f696e746572) { /* descend "on_inter" */
                    buf += 8;
                    w = flatcc_json_parser_symbol_part(buf, end);
                    if ((w & 0xffffff0000000000) == 0x76616c0000000000) { /* "val" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 3);
                        if (mark != buf) {
                            uint32_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    event_local_AzureIoTSecurity_json_parser_enum,
                                    event_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                if (!(pval = flatcc_builder_table_add(ctx->ctx, 2, 4, 4))) goto failed;
                                flatbuffers_uint32_write_to_pe(pval, val);
                            }
                        } else {
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        }
                    } else { /* "val" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* "val" */
                } else { /* descend "on_inter" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* descend "on_inter" */
            } else { /* descend "collecti" */
                if ((w & 0xffff000000000000) == 0x6964000000000000) { /* "id" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                    if (mark != buf) {
                        if (!(pval = flatcc_builder_table_add(ctx->ctx, 0, 16, 1))) goto failed;
                        buf = AzureIoTSecurity_UUID_parse_json_struct_inline(ctx, buf, end, pval);
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "id" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "id" */
            } /* descend "collecti" */
        } else { /* branch "payload" */
            if (w == 0x7061796c6f61645f) { /* descend "payload_" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xffffffff00000000) == 0x7479706500000000) { /* "type" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 4);
                    if (mark != buf) {
                        static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                AzureIoTSecurity_Payload_parse_json_enum,
                                event_local_AzureIoTSecurity_json_parser_enum,
                                event_global_json_parser_enum, 0 };
                        buf = flatcc_json_parser_union_type(ctx, buf, end, 0, 4, h_unions, symbolic_parsers, AzureIoTSecurity_Payload_parse_json_union);
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "type" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "type" */
            } else { /* descend "payload_" */
                if ((w & 0xffffffffffffff00) == 0x7061796c6f616400) { /* "payload" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 7);
                    if (mark != buf) {
                        buf = flatcc_json_parser_union(ctx, buf, end, 0, 4, h_unions, AzureIoTSecurity_Payload_parse_json_union);
                    } else {
                        goto pfguard1;
                    }
                } else { /* "payload" */
                    goto pfguard1;
                } /* "payload" */
                goto endpfguard1;
pfguard1:
                if ((w & 0xffffffff00000000) == 0x74696d6500000000) { /* "time" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 4);
                    if (mark != buf) {
                        uint32_t val = 0;
                        static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                event_local_AzureIoTSecurity_json_parser_enum,
                                event_global_json_parser_enum, 0 };
                        buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                        if (mark == buf) {
                            buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                            if (buf == mark || buf == end) goto failed;
                        }
                        if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                            if (!(pval = flatcc_builder_table_add(ctx->ctx, 1, 4, 4))) goto failed;
                            flatbuffers_uint32_write_to_pe(pval, val);
                        }
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "time" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "time" */
endpfguard1:
                (void)0;
            } /* descend "payload_" */
        } /* branch "payload" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!flatcc_builder_check_required_field(ctx->ctx, 0)
        ||  !flatcc_builder_check_required_field(ctx->ctx, 3)
        ||  !flatcc_builder_check_required_field(ctx->ctx, 3)
    ) {
        buf = flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_required);
        goto failed;
    }
    buf = flatcc_json_parser_finalize_unions(ctx, buf, end, h_unions);
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_Event_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_Event_parse_json_table);
}

static const char *event_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    /* Scope has no enum / union types to look up. */
    return buf; /* unmatched; */
}

static const char *event_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w < 0x50726f6365737345) { /* branch "ProcessE" */
        if ((w & 0xffffffffffffff00) == 0x5061796c6f616400) { /* "Payload" */
            buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 7);
            if (buf != mark) {
                buf = AzureIoTSecurity_Payload_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
            } else {
                return unmatched;
            }
        } else { /* "Payload" */
            if ((w & 0xffffffffff000000) == 0x4c6576656c000000) { /* "Level" */
                buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 5);
                if (buf != mark) {
                    buf = AzureIoTSecurity_Level_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                } else {
                    return unmatched;
                }
            } else { /* "Level" */
                return unmatched;
            } /* "Level" */
        } /* "Payload" */
    } else { /* branch "ProcessE" */
        if (w < 0x50726f746f636f6c) { /* branch "Protocol" */
            if (w == 0x50726f6365737345) { /* descend "ProcessE" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if (w == 0x76656e7454797065) { /* "ventType" */
                    buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
                    if (buf != mark) {
                        buf = AzureIoTSecurity_ProcessEventType_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                    } else {
                        return unmatched;
                    }
                } else { /* "ventType" */
                    return unmatched;
                } /* "ventType" */
            } else { /* descend "ProcessE" */
                return unmatched;
            } /* descend "ProcessE" */
        } else { /* branch "Protocol" */
            if (w < 0x526573756c740000) { /* branch "Result" */
                if (w == 0x50726f746f636f6c) { /* "Protocol" */
                    buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
                    if (buf != mark) {
                        buf = AzureIoTSecurity_Protocol_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                    } else {
                        return unmatched;
                    }
                } else { /* "Protocol" */
                    return unmatched;
                } /* "Protocol" */
            } else { /* branch "Result" */
                if (w == 0x5365766572697479) { /* "Severity" */
                    buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
                    if (buf != mark) {
                        buf = AzureIoTSecurity_Severity_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                    } else {
                        return unmatched;
                    }
                } else { /* "Severity" */
                    if ((w & 0xffffffffffff0000) == 0x526573756c740000) { /* "Result" */
                        buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 6);
                        if (buf != mark) {
                            buf = AzureIoTSecurity_Result_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                        } else {
                            return unmatched;
                        }
                    } else { /* "Result" */
                        return unmatched;
                    } /* "Result" */
                } /* "Severity" */
            } /* branch "Result" */
        } /* branch "Protocol" */
    } /* branch "ProcessE" */
    return buf;
}

static const char *event_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x417a757265496f54) { /* descend "AzureIoT" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x5365637572697479) { /* descend "Security" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if (w < 0x2e50726f63657373) { /* branch ".Process" */
                if (w == 0x2e5061796c6f6164) { /* ".Payload" */
                    buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
                    if (buf != mark) {
                        buf = AzureIoTSecurity_Payload_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                    } else {
                        return unmatched;
                    }
                } else { /* ".Payload" */
                    if ((w & 0xffffffffffff0000) == 0x2e4c6576656c0000) { /* ".Level" */
                        buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 6);
                        if (buf != mark) {
                            buf = AzureIoTSecurity_Level_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                        } else {
                            return unmatched;
                        }
                    } else { /* ".Level" */
                        return unmatched;
                    } /* ".Level" */
                } /* ".Payload" */
            } else { /* branch ".Process" */
                if (w < 0x2e50726f746f636f) { /* branch ".Protoco" */
                    if (w == 0x2e50726f63657373) { /* descend ".Process" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if (w == 0x4576656e74547970) { /* descend "EventTyp" */
                            buf += 8;
                            w = flatcc_json_parser_symbol_part(buf, end);
                            if ((w & 0xff00000000000000) == 0x6500000000000000) { /* "e" */
                                buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                                if (buf != mark) {
                                    buf = AzureIoTSecurity_ProcessEventType_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                                } else {
                                    return unmatched;
                                }
                            } else { /* "e" */
                                return unmatched;
                            } /* "e" */
                        } else { /* descend "EventTyp" */
                            return unmatched;
                        } /* descend "EventTyp" */
                    } else { /* descend ".Process" */
                        return unmatched;
                    } /* descend ".Process" */
                } else { /* branch ".Protoco" */
                    if (w < 0x2e526573756c7400) { /* branch ".Result" */
                        if (w == 0x2e50726f746f636f) { /* descend ".Protoco" */
                            buf += 8;
                            w = flatcc_json_parser_symbol_part(buf, end);
                            if ((w & 0xff00000000000000) == 0x6c00000000000000) { /* "l" */
                                buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                                if (buf != mark) {
                                    buf = AzureIoTSecurity_Protocol_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                                } else {
                                    return unmatched;
                                }
                            } else { /* "l" */
                                return unmatched;
                            } /* "l" */
                        } else { /* descend ".Protoco" */
                            return unmatched;
                        } /* descend ".Protoco" */
                    } else { /* branch ".Result" */
                        if ((w & 0xffffffffffffff00) == 0x2e526573756c7400) { /* ".Result" */
                            buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 7);
                            if (buf != mark) {
                                buf = AzureIoTSecurity_Result_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                            } else {
                                goto pfguard1;
                            }
                        } else { /* ".Result" */
                            goto pfguard1;
                        } /* ".Result" */
                        goto endpfguard1;
pfguard1:
                        if (w == 0x2e53657665726974) { /* descend ".Severit" */
                            buf += 8;
                            w = flatcc_json_parser_symbol_part(buf, end);
                            if ((w & 0xff00000000000000) == 0x7900000000000000) { /* "y" */
                                buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                                if (buf != mark) {
                                    buf = AzureIoTSecurity_Severity_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                                } else {
                                    return unmatched;
                                }
                            } else { /* "y" */
                                return unmatched;
                            } /* "y" */
                        } else { /* descend ".Severit" */
                            return unmatched;
                        } /* descend ".Severit" */
endpfguard1:
                        (void)0;
                    } /* branch ".Result" */
                } /* branch ".Protoco" */
            } /* branch ".Process" */
        } else { /* descend "Security" */
            return unmatched;
        } /* descend "Security" */
    } else { /* descend "AzureIoT" */
        return unmatched;
    } /* descend "AzureIoT" */
    return buf;
}

#include "flatcc/flatcc_epilogue.h"
#endif /* EVENT_JSON_PARSER_H */
