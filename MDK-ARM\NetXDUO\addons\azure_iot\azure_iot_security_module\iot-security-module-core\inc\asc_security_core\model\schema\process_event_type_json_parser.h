#ifndef PROCESS_EVENT_TYPE_JSON_PARSER_H
#define PROCESS_EVENT_TYPE_JSON_PARSER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_parser.h"
#include "flatcc/flatcc_prologue.h"

static const char *AzureIoTSecurity_ProcessEventType_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);
static const char *process_event_type_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *process_event_type_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *process_event_type_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);

static const char *AzureIoTSecurity_ProcessEventType_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_sign, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w < 0x4558495400000000) { /* branch "EXIT" */
        if ((w & 0xffffffff00000000) == 0x4558454300000000) { /* "EXEC" */
            buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 4, aggregate);
            if (buf != mark) {
                *value = UINT64_C(1), *value_sign = 0;
            } else {
                return unmatched;
            }
        } else { /* "EXEC" */
            return unmatched;
        } /* "EXEC" */
    } else { /* branch "EXIT" */
        if ((w & 0xffffffff00000000) == 0x464f524b00000000) { /* "FORK" */
            buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 4, aggregate);
            if (buf != mark) {
                *value = UINT64_C(0), *value_sign = 0;
            } else {
                return unmatched;
            }
        } else { /* "FORK" */
            if ((w & 0xffffffff00000000) == 0x4558495400000000) { /* "EXIT" */
                buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 4, aggregate);
                if (buf != mark) {
                    *value = UINT64_C(2), *value_sign = 0;
                } else {
                    return unmatched;
                }
            } else { /* "EXIT" */
                return unmatched;
            } /* "EXIT" */
        } /* "FORK" */
    } /* branch "EXIT" */
    return buf;
}

static const char *process_event_type_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    /* Scope has no enum / union types to look up. */
    return buf; /* unmatched; */
}

static const char *process_event_type_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x50726f6365737345) { /* descend "ProcessE" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x76656e7454797065) { /* "ventType" */
            buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
            if (buf != mark) {
                buf = AzureIoTSecurity_ProcessEventType_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
            } else {
                return unmatched;
            }
        } else { /* "ventType" */
            return unmatched;
        } /* "ventType" */
    } else { /* descend "ProcessE" */
        return unmatched;
    } /* descend "ProcessE" */
    return buf;
}

static const char *process_event_type_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x417a757265496f54) { /* descend "AzureIoT" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x5365637572697479) { /* descend "Security" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if (w == 0x2e50726f63657373) { /* descend ".Process" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if (w == 0x4576656e74547970) { /* descend "EventTyp" */
                    buf += 8;
                    w = flatcc_json_parser_symbol_part(buf, end);
                    if ((w & 0xff00000000000000) == 0x6500000000000000) { /* "e" */
                        buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                        if (buf != mark) {
                            buf = AzureIoTSecurity_ProcessEventType_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                        } else {
                            return unmatched;
                        }
                    } else { /* "e" */
                        return unmatched;
                    } /* "e" */
                } else { /* descend "EventTyp" */
                    return unmatched;
                } /* descend "EventTyp" */
            } else { /* descend ".Process" */
                return unmatched;
            } /* descend ".Process" */
        } else { /* descend "Security" */
            return unmatched;
        } /* descend "Security" */
    } else { /* descend "AzureIoT" */
        return unmatched;
    } /* descend "AzureIoT" */
    return buf;
}

#include "flatcc/flatcc_epilogue.h"
#endif /* PROCESS_EVENT_TYPE_JSON_PARSER_H */
