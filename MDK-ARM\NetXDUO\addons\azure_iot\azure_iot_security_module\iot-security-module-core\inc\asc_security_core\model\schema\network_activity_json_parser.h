#ifndef NETWORK_ACTIVITY_JSON_PARSER_H
#define NETWORK_ACTIVITY_JSON_PARSER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_parser.h"
#ifndef PROTOCOL_JSON_PARSER_H
#include "protocol_json_parser.h"
#endif
#include "flatcc/flatcc_prologue.h"

static const char *AzureIoTSecurity_IPv4Addresses_parse_json_struct_inline(flatcc_json_parser_t *ctx, const char *buf, const char *end, void *struct_base);
static const char *AzureIoTSecurity_IPv4Addresses_parse_json_struct(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *AzureIoTSecurity_IPv6Addresses_parse_json_struct_inline(flatcc_json_parser_t *ctx, const char *buf, const char *end, void *struct_base);
static const char *AzureIoTSecurity_IPv6Addresses_parse_json_struct(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *AzureIoTSecurity_NetworkActivityCommon_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *AzureIoTSecurity_NetworkActivityV4_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *AzureIoTSecurity_NetworkActivityV6_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *AzureIoTSecurity_NetworkActivity_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *network_activity_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *network_activity_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *network_activity_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);

static const char *AzureIoTSecurity_IPv4Addresses_parse_json_struct_inline(flatcc_json_parser_t *ctx, const char *buf, const char *end, void *struct_base)
{
    int more;
    flatcc_builder_ref_t ref;
    void *pval;
    const char *mark;
    uint64_t w;

    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x6c6f63616c5f6164) { /* descend "local_ad" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if ((w & 0xffffffffff000000) == 0x6472657373000000) { /* "dress" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 5);
                if (mark != buf) {
                    uint32_t val = 0;
                    static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                            network_activity_local_AzureIoTSecurity_json_parser_enum,
                            network_activity_global_json_parser_enum, 0 };
                    pval = (void *)((size_t)struct_base + 0);
                    buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                    if (mark == buf) {
                        buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                        if (buf == mark || buf == end) goto failed;
                    }
                    flatbuffers_uint32_write_to_pe(pval, val);
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "dress" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "dress" */
        } else { /* descend "local_ad" */
            if (w == 0x72656d6f74655f61) { /* descend "remote_a" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xffffffffffff0000) == 0x6464726573730000) { /* "ddress" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 6);
                    if (mark != buf) {
                        uint32_t val = 0;
                        static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                network_activity_local_AzureIoTSecurity_json_parser_enum,
                                network_activity_global_json_parser_enum, 0 };
                        pval = (void *)((size_t)struct_base + 4);
                        buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                        if (mark == buf) {
                            buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                            if (buf == mark || buf == end) goto failed;
                        }
                        flatbuffers_uint32_write_to_pe(pval, val);
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "ddress" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "ddress" */
            } else { /* descend "remote_a" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* descend "remote_a" */
        } /* descend "local_ad" */
        buf = flatcc_json_parser_object_end(ctx, buf, end , &more);
    }
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static const char *AzureIoTSecurity_IPv4Addresses_parse_json_struct(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    void *pval;

    *result = 0;
    if (!(pval = flatcc_builder_start_struct(ctx->ctx, 8, 4))) goto failed;
    buf = AzureIoTSecurity_IPv4Addresses_parse_json_struct_inline(ctx, buf, end, pval);
    if (buf == end || !(*result = flatcc_builder_end_struct(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_IPv4Addresses_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_struct_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_IPv4Addresses_parse_json_struct);
}

static const char *AzureIoTSecurity_IPv6Addresses_parse_json_struct_inline(flatcc_json_parser_t *ctx, const char *buf, const char *end, void *struct_base)
{
    int more;
    flatcc_builder_ref_t ref;
    void *pval;
    const char *mark;
    uint64_t w;

    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x6c6f63616c5f6164) { /* descend "local_ad" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if ((w & 0xffffffffff000000) == 0x6472657373000000) { /* "dress" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 5);
                if (mark != buf) {
                    size_t count = 4;
                    uint32_t *base = (uint32_t *)((size_t)struct_base + 0);
                    buf = flatcc_json_parser_array_start(ctx, buf, end, &more);
                    while (more) {
                        uint32_t val = 0;
                        static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                network_activity_local_AzureIoTSecurity_json_parser_enum,
                                network_activity_global_json_parser_enum, 0 };
                        buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                        if (mark == buf) {
                            buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                            if (buf == mark || buf == end) goto failed;
                        }
                        if (count) {
                            flatbuffers_uint32_write_to_pe(base, val);
                            --count;
                            ++base;
                        } else if (!(ctx->flags & flatcc_json_parser_f_skip_array_overflow)) {
                            return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_array_overflow);
                        }
                        buf = flatcc_json_parser_array_end(ctx, buf, end, &more);
                    }
                    if (count) {
                        if (ctx->flags & flatcc_json_parser_f_reject_array_underflow) {
                            return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_array_underflow);
                        }
                        memset(base, 0, count * sizeof(*base));
                    }
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "dress" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "dress" */
        } else { /* descend "local_ad" */
            if (w == 0x72656d6f74655f61) { /* descend "remote_a" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xffffffffffff0000) == 0x6464726573730000) { /* "ddress" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 6);
                    if (mark != buf) {
                        size_t count = 4;
                        uint32_t *base = (uint32_t *)((size_t)struct_base + 16);
                        buf = flatcc_json_parser_array_start(ctx, buf, end, &more);
                        while (more) {
                            uint32_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    network_activity_local_AzureIoTSecurity_json_parser_enum,
                                    network_activity_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (count) {
                                flatbuffers_uint32_write_to_pe(base, val);
                                --count;
                                ++base;
                            } else if (!(ctx->flags & flatcc_json_parser_f_skip_array_overflow)) {
                                return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_array_overflow);
                            }
                            buf = flatcc_json_parser_array_end(ctx, buf, end, &more);
                        }
                        if (count) {
                            if (ctx->flags & flatcc_json_parser_f_reject_array_underflow) {
                                return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_array_underflow);
                            }
                            memset(base, 0, count * sizeof(*base));
                        }
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "ddress" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "ddress" */
            } else { /* descend "remote_a" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* descend "remote_a" */
        } /* descend "local_ad" */
        buf = flatcc_json_parser_object_end(ctx, buf, end , &more);
    }
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static const char *AzureIoTSecurity_IPv6Addresses_parse_json_struct(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    void *pval;

    *result = 0;
    if (!(pval = flatcc_builder_start_struct(ctx->ctx, 32, 4))) goto failed;
    buf = AzureIoTSecurity_IPv6Addresses_parse_json_struct_inline(ctx, buf, end, pval);
    if (buf == end || !(*result = flatcc_builder_end_struct(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_IPv6Addresses_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_struct_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_IPv6Addresses_parse_json_struct);
}

static const char *AzureIoTSecurity_NetworkActivityCommon_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 9)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w < 0x6c6f63616c5f706f) { /* branch "local_po" */
            if (w < 0x62797465735f6f75) { /* branch "bytes_ou" */
                if (w == 0x62797465735f696e) { /* "bytes_in" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 8);
                    if (mark != buf) {
                        uint32_t val = 0;
                        static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                network_activity_local_AzureIoTSecurity_json_parser_enum,
                                network_activity_global_json_parser_enum, 0 };
                        buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                        if (mark == buf) {
                            buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                            if (buf == mark || buf == end) goto failed;
                        }
                        if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                            if (!(pval = flatcc_builder_table_add(ctx->ctx, 2, 4, 4))) goto failed;
                            flatbuffers_uint32_write_to_pe(pval, val);
                        }
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "bytes_in" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "bytes_in" */
            } else { /* branch "bytes_ou" */
                if (w < 0x636f6d6d616e646c) { /* branch "commandl" */
                    if (w == 0x62797465735f6f75) { /* descend "bytes_ou" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xff00000000000000) == 0x7400000000000000) { /* "t" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 1);
                            if (mark != buf) {
                                uint32_t val = 0;
                                static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                        network_activity_local_AzureIoTSecurity_json_parser_enum,
                                        network_activity_global_json_parser_enum, 0 };
                                buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                                if (mark == buf) {
                                    buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                                    if (buf == mark || buf == end) goto failed;
                                }
                                if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 3, 4, 4))) goto failed;
                                    flatbuffers_uint32_write_to_pe(pval, val);
                                }
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "t" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "t" */
                    } else { /* descend "bytes_ou" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* descend "bytes_ou" */
                } else { /* branch "commandl" */
                    if (w == 0x636f6d6d616e646c) { /* descend "commandl" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xffffff0000000000) == 0x696e650000000000) { /* "ine" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 3);
                            if (mark != buf) {
                                buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                                if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 8))) goto failed;
                                *pref = ref;
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "ine" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "ine" */
                    } else { /* descend "commandl" */
                        if (w == 0x6578656375746162) { /* descend "executab" */
                            buf += 8;
                            w = flatcc_json_parser_symbol_part(buf, end);
                            if ((w & 0xffff000000000000) == 0x6c65000000000000) { /* "le" */
                                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                                if (mark != buf) {
                                    buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                                    if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 7))) goto failed;
                                    *pref = ref;
                                } else {
                                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                                }
                            } else { /* "le" */
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            } /* "le" */
                        } else { /* descend "executab" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* descend "executab" */
                    } /* descend "commandl" */
                } /* branch "commandl" */
            } /* branch "bytes_ou" */
        } else { /* branch "local_po" */
            if (w < 0x70726f746f636f6c) { /* branch "protocol" */
                if (w == 0x6c6f63616c5f706f) { /* descend "local_po" */
                    buf += 8;
                    w = flatcc_json_parser_symbol_part(buf, end);
                    if ((w & 0xffff000000000000) == 0x7274000000000000) { /* "rt" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                        if (mark != buf) {
                            uint16_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    network_activity_local_AzureIoTSecurity_json_parser_enum,
                                    network_activity_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_uint16(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_uint16(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                if (!(pval = flatcc_builder_table_add(ctx->ctx, 0, 2, 2))) goto failed;
                                flatbuffers_uint16_write_to_pe(pval, val);
                            }
                        } else {
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        }
                    } else { /* "rt" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* "rt" */
                } else { /* descend "local_po" */
                    if (w == 0x70726f636573735f) { /* descend "process_" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xffff000000000000) == 0x6964000000000000) { /* "id" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                            if (mark != buf) {
                                uint16_t val = 0;
                                static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                        network_activity_local_AzureIoTSecurity_json_parser_enum,
                                        network_activity_global_json_parser_enum, 0 };
                                buf = flatcc_json_parser_uint16(ctx, (mark = buf), end, &val);
                                if (mark == buf) {
                                    buf = flatcc_json_parser_symbolic_uint16(ctx, (mark = buf), end, symbolic_parsers, &val);
                                    if (buf == mark || buf == end) goto failed;
                                }
                                if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 5, 2, 2))) goto failed;
                                    flatbuffers_uint16_write_to_pe(pval, val);
                                }
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "id" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "id" */
                    } else { /* descend "process_" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* descend "process_" */
                } /* descend "local_po" */
            } else { /* branch "protocol" */
                if (w < 0x72656d6f74655f70) { /* branch "remote_p" */
                    if (w == 0x70726f746f636f6c) { /* "protocol" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 8);
                        if (mark != buf) {
                            int8_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    AzureIoTSecurity_Protocol_parse_json_enum,
                                    network_activity_local_AzureIoTSecurity_json_parser_enum,
                                    network_activity_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_int8(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_int8(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                if (!(pval = flatcc_builder_table_add(ctx->ctx, 4, 1, 1))) goto failed;
                                flatbuffers_int8_write_to_pe(pval, val);
                            }
                        } else {
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        }
                    } else { /* "protocol" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* "protocol" */
                } else { /* branch "remote_p" */
                    if (w == 0x72656d6f74655f70) { /* descend "remote_p" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xffffff0000000000) == 0x6f72740000000000) { /* "ort" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 3);
                            if (mark != buf) {
                                uint16_t val = 0;
                                static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                        network_activity_local_AzureIoTSecurity_json_parser_enum,
                                        network_activity_global_json_parser_enum, 0 };
                                buf = flatcc_json_parser_uint16(ctx, (mark = buf), end, &val);
                                if (mark == buf) {
                                    buf = flatcc_json_parser_symbolic_uint16(ctx, (mark = buf), end, symbolic_parsers, &val);
                                    if (buf == mark || buf == end) goto failed;
                                }
                                if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 1, 2, 2))) goto failed;
                                    flatbuffers_uint16_write_to_pe(pval, val);
                                }
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "ort" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "ort" */
                    } else { /* descend "remote_p" */
                        if ((w & 0xffffffffffffff00) == 0x757365725f696400) { /* "user_id" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 7);
                            if (mark != buf) {
                                uint16_t val = 0;
                                static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                        network_activity_local_AzureIoTSecurity_json_parser_enum,
                                        network_activity_global_json_parser_enum, 0 };
                                buf = flatcc_json_parser_uint16(ctx, (mark = buf), end, &val);
                                if (mark == buf) {
                                    buf = flatcc_json_parser_symbolic_uint16(ctx, (mark = buf), end, symbolic_parsers, &val);
                                    if (buf == mark || buf == end) goto failed;
                                }
                                if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 6, 2, 2))) goto failed;
                                    flatbuffers_uint16_write_to_pe(pval, val);
                                }
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "user_id" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "user_id" */
                    } /* descend "remote_p" */
                } /* branch "remote_p" */
            } /* branch "protocol" */
        } /* branch "local_po" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_NetworkActivityCommon_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_NetworkActivityCommon_parse_json_table);
}

static const char *AzureIoTSecurity_NetworkActivityV4_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 2)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x6164647265737365) { /* descend "addresse" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if ((w & 0xff00000000000000) == 0x7300000000000000) { /* "s" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 1);
                if (mark != buf) {
                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 0, 8, 4))) goto failed;
                    buf = AzureIoTSecurity_IPv4Addresses_parse_json_struct_inline(ctx, buf, end, pval);
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "s" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "s" */
        } else { /* descend "addresse" */
            if ((w & 0xffffffffffff0000) == 0x636f6d6d6f6e0000) { /* "common" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 6);
                if (mark != buf) {
                    buf = AzureIoTSecurity_NetworkActivityCommon_parse_json_table(ctx, buf, end, &ref);
                    if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 1))) goto failed;
                    *pref = ref;
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "common" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "common" */
        } /* descend "addresse" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!flatcc_builder_check_required_field(ctx->ctx, 0)
        ||  !flatcc_builder_check_required_field(ctx->ctx, 1)
    ) {
        buf = flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_required);
        goto failed;
    }
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_NetworkActivityV4_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_NetworkActivityV4_parse_json_table);
}

static const char *AzureIoTSecurity_NetworkActivityV6_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 2)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x6164647265737365) { /* descend "addresse" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if ((w & 0xff00000000000000) == 0x7300000000000000) { /* "s" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 1);
                if (mark != buf) {
                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 0, 32, 4))) goto failed;
                    buf = AzureIoTSecurity_IPv6Addresses_parse_json_struct_inline(ctx, buf, end, pval);
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "s" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "s" */
        } else { /* descend "addresse" */
            if ((w & 0xffffffffffff0000) == 0x636f6d6d6f6e0000) { /* "common" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 6);
                if (mark != buf) {
                    buf = AzureIoTSecurity_NetworkActivityCommon_parse_json_table(ctx, buf, end, &ref);
                    if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 1))) goto failed;
                    *pref = ref;
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "common" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "common" */
        } /* descend "addresse" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!flatcc_builder_check_required_field(ctx->ctx, 0)
        ||  !flatcc_builder_check_required_field(ctx->ctx, 1)
    ) {
        buf = flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_required);
        goto failed;
    }
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_NetworkActivityV6_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_NetworkActivityV6_parse_json_table);
}

static const char *AzureIoTSecurity_NetworkActivity_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 2)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x697076345f616374) { /* descend "ipv4_act" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if ((w & 0xffffffffff000000) == 0x6976697479000000) { /* "ivity" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 5);
                if (mark != buf) {
                    if (flatcc_builder_start_offset_vector(ctx->ctx)) goto failed;
                    buf = flatcc_json_parser_array_start(ctx, buf, end, &more);
                    while (more) {
                        buf = AzureIoTSecurity_NetworkActivityV4_parse_json_table(ctx, buf, end, &ref);
                        if (!ref || !(pref = flatcc_builder_extend_offset_vector(ctx->ctx, 1))) goto failed;
                        *pref = ref;
                        buf = flatcc_json_parser_array_end(ctx, buf, end, &more);
                    }
                    ref = flatcc_builder_end_offset_vector(ctx->ctx);
                    if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 0))) goto failed;
                    *pref = ref;
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "ivity" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "ivity" */
        } else { /* descend "ipv4_act" */
            if (w == 0x697076365f616374) { /* descend "ipv6_act" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xffffffffff000000) == 0x6976697479000000) { /* "ivity" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 5);
                    if (mark != buf) {
                        if (flatcc_builder_start_offset_vector(ctx->ctx)) goto failed;
                        buf = flatcc_json_parser_array_start(ctx, buf, end, &more);
                        while (more) {
                            buf = AzureIoTSecurity_NetworkActivityV6_parse_json_table(ctx, buf, end, &ref);
                            if (!ref || !(pref = flatcc_builder_extend_offset_vector(ctx->ctx, 1))) goto failed;
                            *pref = ref;
                            buf = flatcc_json_parser_array_end(ctx, buf, end, &more);
                        }
                        ref = flatcc_builder_end_offset_vector(ctx->ctx);
                        if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 1))) goto failed;
                        *pref = ref;
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "ivity" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "ivity" */
            } else { /* descend "ipv6_act" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* descend "ipv6_act" */
        } /* descend "ipv4_act" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_NetworkActivity_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_NetworkActivity_parse_json_table);
}

static const char *network_activity_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    /* Scope has no enum / union types to look up. */
    return buf; /* unmatched; */
}

static const char *network_activity_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x50726f746f636f6c) { /* "Protocol" */
        buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
        if (buf != mark) {
            buf = AzureIoTSecurity_Protocol_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
        } else {
            return unmatched;
        }
    } else { /* "Protocol" */
        return unmatched;
    } /* "Protocol" */
    return buf;
}

static const char *network_activity_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x417a757265496f54) { /* descend "AzureIoT" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x5365637572697479) { /* descend "Security" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if (w == 0x2e50726f746f636f) { /* descend ".Protoco" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xff00000000000000) == 0x6c00000000000000) { /* "l" */
                    buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                    if (buf != mark) {
                        buf = AzureIoTSecurity_Protocol_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                    } else {
                        return unmatched;
                    }
                } else { /* "l" */
                    return unmatched;
                } /* "l" */
            } else { /* descend ".Protoco" */
                return unmatched;
            } /* descend ".Protoco" */
        } else { /* descend "Security" */
            return unmatched;
        } /* descend "Security" */
    } else { /* descend "AzureIoT" */
        return unmatched;
    } /* descend "AzureIoT" */
    return buf;
}

#include "flatcc/flatcc_epilogue.h"
#endif /* NETWORK_ACTIVITY_JSON_PARSER_H */
