#ifndef PROCESS_JSON_PARSER_H
#define PROCESS_JSON_PARSER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_parser.h"
#ifndef PROCESS_EVENT_TYPE_JSON_PARSER_H
#include "process_event_type_json_parser.h"
#endif
#include "flatcc/flatcc_prologue.h"

static const char *AzureIoTSecurity_Process_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *process_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *process_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *process_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);

static const char *AzureIoTSecurity_Process_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 8)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w < 0x6869745f636f756e) { /* branch "hit_coun" */
            if (w < 0x6576656e745f7479) { /* branch "event_ty" */
                if (w == 0x636f6d6d616e646c) { /* descend "commandl" */
                    buf += 8;
                    w = flatcc_json_parser_symbol_part(buf, end);
                    if ((w & 0xffffff0000000000) == 0x696e650000000000) { /* "ine" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 3);
                        if (mark != buf) {
                            buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                            if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 5))) goto failed;
                            *pref = ref;
                        } else {
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        }
                    } else { /* "ine" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* "ine" */
                } else { /* descend "commandl" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* descend "commandl" */
            } else { /* branch "event_ty" */
                if (w == 0x6576656e745f7479) { /* descend "event_ty" */
                    buf += 8;
                    w = flatcc_json_parser_symbol_part(buf, end);
                    if ((w & 0xffff000000000000) == 0x7065000000000000) { /* "pe" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                        if (mark != buf) {
                            int8_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    AzureIoTSecurity_ProcessEventType_parse_json_enum,
                                    process_local_AzureIoTSecurity_json_parser_enum,
                                    process_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_int8(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_int8(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                if (!(pval = flatcc_builder_table_add(ctx->ctx, 6, 1, 1))) goto failed;
                                flatbuffers_int8_write_to_pe(pval, val);
                            }
                        } else {
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        }
                    } else { /* "pe" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* "pe" */
                } else { /* descend "event_ty" */
                    if (w == 0x6578656375746162) { /* descend "executab" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xffff000000000000) == 0x6c65000000000000) { /* "le" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                            if (mark != buf) {
                                buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                                if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 0))) goto failed;
                                *pref = ref;
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "le" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "le" */
                    } else { /* descend "executab" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* descend "executab" */
                } /* descend "event_ty" */
            } /* branch "event_ty" */
        } else { /* branch "hit_coun" */
            if (w < 0x70726f636573735f) { /* branch "process_" */
                if (w == 0x6869745f636f756e) { /* descend "hit_coun" */
                    buf += 8;
                    w = flatcc_json_parser_symbol_part(buf, end);
                    if ((w & 0xff00000000000000) == 0x7400000000000000) { /* "t" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 1);
                        if (mark != buf) {
                            uint32_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    process_local_AzureIoTSecurity_json_parser_enum,
                                    process_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (val != 1 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                if (!(pval = flatcc_builder_table_add(ctx->ctx, 7, 4, 4))) goto failed;
                                flatbuffers_uint32_write_to_pe(pval, val);
                            }
                        } else {
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        }
                    } else { /* "t" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* "t" */
                } else { /* descend "hit_coun" */
                    if (w == 0x706172656e745f70) { /* descend "parent_p" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if (w == 0x726f636573735f69) { /* descend "rocess_i" */
                            buf += 8;
                            w = flatcc_json_parser_symbol_part(buf, end);
                            if ((w & 0xff00000000000000) == 0x6400000000000000) { /* "d" */
                                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 1);
                                if (mark != buf) {
                                    uint32_t val = 0;
                                    static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                            process_local_AzureIoTSecurity_json_parser_enum,
                                            process_global_json_parser_enum, 0 };
                                    buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                                    if (mark == buf) {
                                        buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                                        if (buf == mark || buf == end) goto failed;
                                    }
                                    if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                        if (!(pval = flatcc_builder_table_add(ctx->ctx, 2, 4, 4))) goto failed;
                                        flatbuffers_uint32_write_to_pe(pval, val);
                                    }
                                } else {
                                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                                }
                            } else { /* "d" */
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            } /* "d" */
                        } else { /* descend "rocess_i" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* descend "rocess_i" */
                    } else { /* descend "parent_p" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* descend "parent_p" */
                } /* descend "hit_coun" */
            } else { /* branch "process_" */
                if (w < 0x757365725f696400) { /* branch "user_id" */
                    if (w == 0x70726f636573735f) { /* descend "process_" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xffff000000000000) == 0x6964000000000000) { /* "id" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                            if (mark != buf) {
                                uint32_t val = 0;
                                static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                        process_local_AzureIoTSecurity_json_parser_enum,
                                        process_global_json_parser_enum, 0 };
                                buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                                if (mark == buf) {
                                    buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                                    if (buf == mark || buf == end) goto failed;
                                }
                                if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 1, 4, 4))) goto failed;
                                    flatbuffers_uint32_write_to_pe(pval, val);
                                }
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "id" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "id" */
                    } else { /* descend "process_" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* descend "process_" */
                } else { /* branch "user_id" */
                    if ((w & 0xffffffffffffff00) == 0x757365725f696400) { /* "user_id" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 7);
                        if (mark != buf) {
                            uint32_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    process_local_AzureIoTSecurity_json_parser_enum,
                                    process_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_uint32(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_uint32(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                if (!(pval = flatcc_builder_table_add(ctx->ctx, 3, 4, 4))) goto failed;
                                flatbuffers_uint32_write_to_pe(pval, val);
                            }
                        } else {
                            goto pfguard1;
                        }
                    } else { /* "user_id" */
                        goto pfguard1;
                    } /* "user_id" */
                    goto endpfguard1;
pfguard1:
                    if (w == 0x757365725f6e616d) { /* descend "user_nam" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xff00000000000000) == 0x6500000000000000) { /* "e" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 1);
                            if (mark != buf) {
                                buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                                if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 4))) goto failed;
                                *pref = ref;
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "e" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "e" */
                    } else { /* descend "user_nam" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* descend "user_nam" */
endpfguard1:
                    (void)0;
                } /* branch "user_id" */
            } /* branch "process_" */
        } /* branch "hit_coun" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_Process_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_Process_parse_json_table);
}

static const char *process_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    /* Scope has no enum / union types to look up. */
    return buf; /* unmatched; */
}

static const char *process_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x50726f6365737345) { /* descend "ProcessE" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x76656e7454797065) { /* "ventType" */
            buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
            if (buf != mark) {
                buf = AzureIoTSecurity_ProcessEventType_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
            } else {
                return unmatched;
            }
        } else { /* "ventType" */
            return unmatched;
        } /* "ventType" */
    } else { /* descend "ProcessE" */
        return unmatched;
    } /* descend "ProcessE" */
    return buf;
}

static const char *process_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x417a757265496f54) { /* descend "AzureIoT" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x5365637572697479) { /* descend "Security" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if (w == 0x2e50726f63657373) { /* descend ".Process" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if (w == 0x4576656e74547970) { /* descend "EventTyp" */
                    buf += 8;
                    w = flatcc_json_parser_symbol_part(buf, end);
                    if ((w & 0xff00000000000000) == 0x6500000000000000) { /* "e" */
                        buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                        if (buf != mark) {
                            buf = AzureIoTSecurity_ProcessEventType_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                        } else {
                            return unmatched;
                        }
                    } else { /* "e" */
                        return unmatched;
                    } /* "e" */
                } else { /* descend "EventTyp" */
                    return unmatched;
                } /* descend "EventTyp" */
            } else { /* descend ".Process" */
                return unmatched;
            } /* descend ".Process" */
        } else { /* descend "Security" */
            return unmatched;
        } /* descend "Security" */
    } else { /* descend "AzureIoT" */
        return unmatched;
    } /* descend "AzureIoT" */
    return buf;
}

#include "flatcc/flatcc_epilogue.h"
#endif /* PROCESS_JSON_PARSER_H */
