#ifndef PROTOCOL_JSON_PARSER_H
#define PROTOCOL_JSON_PARSER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_parser.h"
#include "flatcc/flatcc_prologue.h"

static const char *AzureIoTSecurity_Protocol_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);
static const char *protocol_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *protocol_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *protocol_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);

static const char *AzureIoTSecurity_Protocol_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_sign, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w < 0x5443500000000000) { /* branch "TCP" */
        if ((w & 0xffffffff00000000) == 0x49434d5000000000) { /* "ICMP" */
            buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 4, aggregate);
            if (buf != mark) {
                *value = UINT64_C(2), *value_sign = 0;
            } else {
                return unmatched;
            }
        } else { /* "ICMP" */
            return unmatched;
        } /* "ICMP" */
    } else { /* branch "TCP" */
        if ((w & 0xffffff0000000000) == 0x5544500000000000) { /* "UDP" */
            buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 3, aggregate);
            if (buf != mark) {
                *value = UINT64_C(1), *value_sign = 0;
            } else {
                return unmatched;
            }
        } else { /* "UDP" */
            if ((w & 0xffffff0000000000) == 0x5443500000000000) { /* "TCP" */
                buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 3, aggregate);
                if (buf != mark) {
                    *value = UINT64_C(0), *value_sign = 0;
                } else {
                    return unmatched;
                }
            } else { /* "TCP" */
                return unmatched;
            } /* "TCP" */
        } /* "UDP" */
    } /* branch "TCP" */
    return buf;
}

static const char *protocol_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    /* Scope has no enum / union types to look up. */
    return buf; /* unmatched; */
}

static const char *protocol_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x50726f746f636f6c) { /* "Protocol" */
        buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
        if (buf != mark) {
            buf = AzureIoTSecurity_Protocol_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
        } else {
            return unmatched;
        }
    } else { /* "Protocol" */
        return unmatched;
    } /* "Protocol" */
    return buf;
}

static const char *protocol_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x417a757265496f54) { /* descend "AzureIoT" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x5365637572697479) { /* descend "Security" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if (w == 0x2e50726f746f636f) { /* descend ".Protoco" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xff00000000000000) == 0x6c00000000000000) { /* "l" */
                    buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                    if (buf != mark) {
                        buf = AzureIoTSecurity_Protocol_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                    } else {
                        return unmatched;
                    }
                } else { /* "l" */
                    return unmatched;
                } /* "l" */
            } else { /* descend ".Protoco" */
                return unmatched;
            } /* descend ".Protoco" */
        } else { /* descend "Security" */
            return unmatched;
        } /* descend "Security" */
    } else { /* descend "AzureIoT" */
        return unmatched;
    } /* descend "AzureIoT" */
    return buf;
}

#include "flatcc/flatcc_epilogue.h"
#endif /* PROTOCOL_JSON_PARSER_H */
