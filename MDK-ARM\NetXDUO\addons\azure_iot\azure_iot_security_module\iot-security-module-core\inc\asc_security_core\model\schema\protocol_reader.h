#ifndef PROTOCOL_READER_H
#define PROTOCOL_READER_H

/* Generated by flatcc 0.6.1-dev <PERSON>uffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif



typedef int8_t AzureIoTSecurity_Protocol_enum_t;
__flatbuffers_define_integer_type(AzureIoTSecurity_Protocol, AzureIoTSecurity_Protocol_enum_t, 8)
#define AzureIoTSecurity_Protocol_TCP ((AzureIoTSecurity_Protocol_enum_t)INT8_C(0))
#define AzureIoTSecurity_Protocol_UDP ((AzureIoTSecurity_Protocol_enum_t)INT8_C(1))
#define AzureIoTSecurity_Protocol_ICMP ((AzureIoTSecurity_Protocol_enum_t)INT8_C(2))

static inline const char *AzureIoTSecurity_Protocol_name(AzureIoTSecurity_Protocol_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_Protocol_TCP: return "TCP";
    case AzureIoTSecurity_Protocol_UDP: return "UDP";
    case AzureIoTSecurity_Protocol_ICMP: return "ICMP";
    default: return "";
    }
}

static inline int AzureIoTSecurity_Protocol_is_known_value(AzureIoTSecurity_Protocol_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_Protocol_TCP: return 1;
    case AzureIoTSecurity_Protocol_UDP: return 1;
    case AzureIoTSecurity_Protocol_ICMP: return 1;
    default: return 0;
    }
}




#include "flatcc/flatcc_epilogue.h"
#endif /* PROTOCOL_READER_H */
