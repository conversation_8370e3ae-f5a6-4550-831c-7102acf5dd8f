#ifndef PAYLOAD_READER_H
#define PAYLOAD_READER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#ifndef LOG_READER_H
#include "log_reader.h"
#endif
#ifndef PROCESS_READER_H
#include "process_reader.h"
#endif
#ifndef BASELINE_READER_H
#include "baseline_reader.h"
#endif
#ifndef HEARTBEAT_READER_H
#include "heartbeat_reader.h"
#endif
#ifndef LISTENING_PORTS_READER_H
#include "listening_ports_reader.h"
#endif
#ifndef SYSTEM_INFORMATION_READER_H
#include "system_information_reader.h"
#endif
#ifndef NETWORK_ACTIVITY_READER_H
#include "network_activity_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif




typedef uint8_t AzureIoTSecurity_Payload_union_type_t;
__flatbuffers_define_integer_type(AzureIoTSecurity_Payload, AzureIoTSecurity_Payload_union_type_t, 8)
__flatbuffers_define_union(flatbuffers_, AzureIoTSecurity_Payload)
#define AzureIoTSecurity_Payload_NONE ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(0))
#define AzureIoTSecurity_Payload_NetworkActivity ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(1))
#define AzureIoTSecurity_Payload_SystemInformation ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(2))
#define AzureIoTSecurity_Payload_ListeningPorts ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(3))
#define AzureIoTSecurity_Payload_Heartbeat ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(4))
#define AzureIoTSecurity_Payload_Baseline ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(5))
#define AzureIoTSecurity_Payload_Process ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(6))
#define AzureIoTSecurity_Payload_Log ((AzureIoTSecurity_Payload_union_type_t)UINT8_C(7))

static inline const char *AzureIoTSecurity_Payload_type_name(AzureIoTSecurity_Payload_union_type_t type)
{
    switch (type) {
    case AzureIoTSecurity_Payload_NONE: return "NONE";
    case AzureIoTSecurity_Payload_NetworkActivity: return "NetworkActivity";
    case AzureIoTSecurity_Payload_SystemInformation: return "SystemInformation";
    case AzureIoTSecurity_Payload_ListeningPorts: return "ListeningPorts";
    case AzureIoTSecurity_Payload_Heartbeat: return "Heartbeat";
    case AzureIoTSecurity_Payload_Baseline: return "Baseline";
    case AzureIoTSecurity_Payload_Process: return "Process";
    case AzureIoTSecurity_Payload_Log: return "Log";
    default: return "";
    }
}

static inline int AzureIoTSecurity_Payload_is_known_type(AzureIoTSecurity_Payload_union_type_t type)
{
    switch (type) {
    case AzureIoTSecurity_Payload_NONE: return 1;
    case AzureIoTSecurity_Payload_NetworkActivity: return 1;
    case AzureIoTSecurity_Payload_SystemInformation: return 1;
    case AzureIoTSecurity_Payload_ListeningPorts: return 1;
    case AzureIoTSecurity_Payload_Heartbeat: return 1;
    case AzureIoTSecurity_Payload_Baseline: return 1;
    case AzureIoTSecurity_Payload_Process: return 1;
    case AzureIoTSecurity_Payload_Log: return 1;
    default: return 0;
    }
}



#include "flatcc/flatcc_epilogue.h"
#endif /* PAYLOAD_READER_H */
