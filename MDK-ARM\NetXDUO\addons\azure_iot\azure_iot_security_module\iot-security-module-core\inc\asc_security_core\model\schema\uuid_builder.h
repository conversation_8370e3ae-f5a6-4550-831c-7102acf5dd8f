#ifndef UUID_BUILDER_H
#define UUID_BUILDER_H

/* Generated by flatcc 0.6.1-dev <PERSON>uffers schema compiler for C by dvide.com */

#ifndef UUID_READER_H
#include "uuid_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

#define __AzureIoTSecurity_UUID_formal_args , const uint8_t v0[16]
#define __AzureIoTSecurity_UUID_call_args , v0
static inline AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_assign(AzureIoTSecurity_UUID_t *p, const uint8_t v0[16])
{ flatbuffers_uint8_array_copy(p->value, v0, 16);
  return p; }
static inline AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_copy(AzureIoTSecurity_UUID_t *p, const AzureIoTSecurity_UUID_t *p2)
{ flatbuffers_uint8_array_copy(p->value, p2->value, 16);
  return p; }
static inline AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_assign_to_pe(AzureIoTSecurity_UUID_t *p, const uint8_t v0[16])
{ flatbuffers_uint8_array_copy_to_pe(p->value, v0, 16);
  return p; }
static inline AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_copy_to_pe(AzureIoTSecurity_UUID_t *p, const AzureIoTSecurity_UUID_t *p2)
{ flatbuffers_uint8_array_copy_to_pe(p->value, p2->value, 16);
  return p; }
static inline AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_assign_from_pe(AzureIoTSecurity_UUID_t *p, const uint8_t v0[16])
{ flatbuffers_uint8_array_copy_from_pe(p->value, v0, 16);
  return p; }
static inline AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_copy_from_pe(AzureIoTSecurity_UUID_t *p, const AzureIoTSecurity_UUID_t *p2)
{ flatbuffers_uint8_array_copy_from_pe(p->value, p2->value, 16);
  return p; }
__flatbuffers_build_struct(flatbuffers_, AzureIoTSecurity_UUID, 16, 1, AzureIoTSecurity_UUID_file_identifier, AzureIoTSecurity_UUID_type_identifier)
__flatbuffers_define_fixed_array_primitives(flatbuffers_, AzureIoTSecurity_UUID, AzureIoTSecurity_UUID_t)

#include "flatcc/flatcc_epilogue.h"
#endif /* UUID_BUILDER_H */
