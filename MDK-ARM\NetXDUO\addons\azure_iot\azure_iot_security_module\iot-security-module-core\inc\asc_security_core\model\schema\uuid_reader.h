#ifndef UUID_READER_H
#define UUID_READER_H

/* Generated by flatcc 0.6.1-dev <PERSON>uffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

typedef struct AzureIoTSecurity_UUID AzureIoTSecurity_UUID_t;
typedef const AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_struct_t;
typedef AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_mutable_struct_t;
typedef const AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_vec_t;
typedef AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID_mutable_vec_t;

#ifndef AzureIoTSecurity_UUID_file_identifier
#define AzureIoTSecurity_UUID_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_UUID_file_identifier */
#ifndef AzureIoTSecurity_UUID_identifier
#define AzureIoTSecurity_UUID_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_UUID_type_hash ((flatbuffers_thash_t)0xd8ac5903)
#define AzureIoTSecurity_UUID_type_identifier "\x03\x59\xac\xd8"


struct AzureIoTSecurity_UUID {
    /**  A UUID v4 value (128 bits) */
    alignas(1) uint8_t value[16];
};
static_assert(sizeof(AzureIoTSecurity_UUID_t) == 16, "struct size mismatch");

static inline const AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID__const_ptr_add(const AzureIoTSecurity_UUID_t *p, size_t i) { return p + i; }
static inline AzureIoTSecurity_UUID_t *AzureIoTSecurity_UUID__ptr_add(AzureIoTSecurity_UUID_t *p, size_t i) { return p + i; }
static inline AzureIoTSecurity_UUID_struct_t AzureIoTSecurity_UUID_vec_at(AzureIoTSecurity_UUID_vec_t vec, size_t i)
__flatbuffers_struct_vec_at(vec, i)
static inline size_t AzureIoTSecurity_UUID__size(void) { return 16; }
static inline size_t AzureIoTSecurity_UUID_vec_len(AzureIoTSecurity_UUID_vec_t vec)
__flatbuffers_vec_len(vec)
__flatbuffers_struct_as_root(AzureIoTSecurity_UUID)

__flatbuffers_define_struct_scalar_fixed_array_field(AzureIoTSecurity_UUID, value, flatbuffers_uint8, uint8_t, 16)



#include "flatcc/flatcc_epilogue.h"
#endif /* UUID_READER_H */
