#ifndef LISTENING_PORTS_JSON_PRINTER_H
#define LISTENING_PORTS_JSON_PRINTER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_printer.h"
#ifndef PROTOCOL_JSON_PRINTER_H
#include "protocol_json_printer.h"
#endif
#include "flatcc/flatcc_prologue.h"

static void AzureIoTSecurity_ListeningPortsCommon_print_json_struct(flatcc_json_printer_t *ctx, const void *p);
static void AzureIoTSecurity_ListeningPortsV4_print_json_struct(flatcc_json_printer_t *ctx, const void *p);
static void AzureIoTSecurity_ListeningPortsV6_print_json_struct(flatcc_json_printer_t *ctx, const void *p);
static void AzureIoTSecurity_ListeningPorts_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td);

static void AzureIoTSecurity_ListeningPortsCommon_print_json_struct(flatcc_json_printer_t *ctx, const void *p)
{
    flatcc_json_printer_uint16_struct_field(ctx, 0, p, 0, "local_port", 10);
    flatcc_json_printer_int8_enum_struct_field(ctx, 1, p, 2, "protocol", 8, AzureIoTSecurity_Protocol_print_json_enum);
}

static inline int AzureIoTSecurity_ListeningPortsCommon_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_struct_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_ListeningPortsCommon_print_json_struct);
}

static void AzureIoTSecurity_ListeningPortsV4_print_json_struct(flatcc_json_printer_t *ctx, const void *p)
{
    flatcc_json_printer_uint32_struct_field(ctx, 0, p, 0, "local_address", 13);
    flatcc_json_printer_embedded_struct_field(ctx, 1, p, 4, "common", 6, AzureIoTSecurity_ListeningPortsCommon_print_json_struct);
}

static inline int AzureIoTSecurity_ListeningPortsV4_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_struct_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_ListeningPortsV4_print_json_struct);
}

static void AzureIoTSecurity_ListeningPortsV6_print_json_struct(flatcc_json_printer_t *ctx, const void *p)
{
    flatcc_json_printer_uint32_array_struct_field(ctx, 0, p, 0, "local_address", 13, 4);
    flatcc_json_printer_embedded_struct_field(ctx, 1, p, 16, "common", 6, AzureIoTSecurity_ListeningPortsCommon_print_json_struct);
}

static inline int AzureIoTSecurity_ListeningPortsV6_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_struct_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_ListeningPortsV6_print_json_struct);
}

static void AzureIoTSecurity_ListeningPorts_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td)
{
    flatcc_json_printer_struct_vector_field(ctx, td, 0, "ipv4_ports", 10, 8, AzureIoTSecurity_ListeningPortsV4_print_json_struct);
    flatcc_json_printer_struct_vector_field(ctx, td, 1, "ipv6_ports", 10, 20, AzureIoTSecurity_ListeningPortsV6_print_json_struct);
}

static inline int AzureIoTSecurity_ListeningPorts_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_table_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_ListeningPorts_print_json_table);
}

#include "flatcc/flatcc_epilogue.h"
#endif /* LISTENING_PORTS_JSON_PRINTER_H */
