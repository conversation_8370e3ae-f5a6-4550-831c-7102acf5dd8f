#ifndef PROCESS_EVENT_BUILDER_H
#define PROCESS_EVENT_BUILDER_H

/* Generated by flatcc 0.6.1-dev <PERSON><PERSON>uff<PERSON> schema compiler for C by dvide.com */

#ifndef PROCESS_EVENT_READER_H
#include "process_event_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

#define __AzureIoTSecurity_ProcessEvent_formal_args , AzureIoTSecurity_ProcessEvent_enum_t v0
#define __AzureIoTSecurity_ProcessEvent_call_args , v0
__flatbuffers_build_scalar(flatbuffers_, AzureIoTSecurity_ProcessEvent, AzureIoTSecurity_ProcessEvent_enum_t)

#include "flatcc/flatcc_epilogue.h"
#endif /* PROCESS_EVENT_BUILDER_H */
