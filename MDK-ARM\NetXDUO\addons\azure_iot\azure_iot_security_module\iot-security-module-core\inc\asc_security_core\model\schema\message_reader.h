#ifndef MESSAGE_READER_H
#define MESSAGE_READER_H

/* Generated by flatcc 0.6.1-dev <PERSON><PERSON>uffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#ifndef EVENT_READER_H
#include "event_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif


typedef const struct AzureIoTSecurity_Message_table *AzureIoTSecurity_Message_table_t;
typedef struct AzureIoTSecurity_Message_table *AzureIoTSecurity_Message_mutable_table_t;
typedef const flatbuffers_uoffset_t *AzureIoTSecurity_Message_vec_t;
typedef flatbuffers_uoffset_t *AzureIoTSecurity_Message_mutable_vec_t;
#ifndef AzureIoTSecurity_Message_file_identifier
#define AzureIoTSecurity_Message_file_identifier flatbuffers_identifier
#endif
/* deprecated, use AzureIoTSecurity_Message_file_identifier */
#ifndef AzureIoTSecurity_Message_identifier
#define AzureIoTSecurity_Message_identifier flatbuffers_identifier
#endif
#define AzureIoTSecurity_Message_type_hash ((flatbuffers_thash_t)0x7ec645e7)
#define AzureIoTSecurity_Message_type_identifier "\xe7\x45\xc6\x7e"



struct AzureIoTSecurity_Message_table { uint8_t unused__; };

static inline size_t AzureIoTSecurity_Message_vec_len(AzureIoTSecurity_Message_vec_t vec)
__flatbuffers_vec_len(vec)
static inline AzureIoTSecurity_Message_table_t AzureIoTSecurity_Message_vec_at(AzureIoTSecurity_Message_vec_t vec, size_t i)
__flatbuffers_offset_vec_at(AzureIoTSecurity_Message_table_t, vec, i, 0)
__flatbuffers_table_as_root(AzureIoTSecurity_Message)

/**  Security Module ID - A unique identifier of the device */
__flatbuffers_define_string_field(0, AzureIoTSecurity_Message, security_module_id, 1)
/**  Security Module Version */
__flatbuffers_define_scalar_field(1, AzureIoTSecurity_Message, security_module_version, flatbuffers_uint32, uint32_t, UINT32_C(0))
/**  Time-Zone, an integer between [-11, +12] (inclusive) */
__flatbuffers_define_scalar_field(2, AzureIoTSecurity_Message, timezone, flatbuffers_int8, int8_t, INT8_C(0))
/**  Security Events */
__flatbuffers_define_vector_field(3, AzureIoTSecurity_Message, events, AzureIoTSecurity_Event_vec_t, 1)


#include "flatcc/flatcc_epilogue.h"
#endif /* MESSAGE_READER_H */
