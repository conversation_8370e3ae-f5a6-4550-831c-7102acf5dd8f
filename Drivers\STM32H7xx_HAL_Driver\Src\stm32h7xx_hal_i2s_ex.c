/**
  ******************************************************************************
  * @file    stm32h7xx_hal_i2s_ex.c
  * <AUTHOR> Application Team
  * @brief   I2S HAL module driver.
  *          This file provides firmware functions to manage the following
  *          functionalities of I2S extension peripheral:
  *           + Extension features Functions
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/**
  ******************************************************************************
                      ===== I2S FULL DUPLEX FEATURE =====
       I2S Full Duplex APIs are available in stm32h7xx_hal_i2s.c/.h
  ******************************************************************************
  */




