#include "User_Header.h"

static UCHAR tx_byte_pool_buffer[TX_APP_MEM_POOL_SIZE];
static TX_BYTE_POOL tx_app_byte_pool;
/** 线程0宏定义 **/
#define THREAD0_PRIO 		 5u
#define THREAD0_PRIO_VAL 5u
#define THREAD0_STK_SIZE 256u
static TX_THREAD Thread0_TCB;
static u8	 Thread0_STK[THREAD0_STK_SIZE];

int main(void){
	/* 系统初始化 */
  System_Init();
	
	/* 关闭时间基准，即关闭中断系统 */
	HAL_SuspendTick();
	
	/* 启动线程 */
  MX_ThreadX_Init();
  while (1){
		
  }
}


VOID tx_application_define(VOID *first_unused_memory){
	VOID *memory_ptr;
  if (tx_byte_pool_create(&tx_app_byte_pool, "Tx App memory pool", tx_byte_pool_buffer, TX_APP_MEM_POOL_SIZE) != TX_SUCCESS){}
  else{
    memory_ptr = (VOID *)&tx_app_byte_pool;
    if (App_ThreadX_Init(memory_ptr) != TX_SUCCESS){}
		/* 创建线程  */
		// 线程0 启动任务
		tx_thread_create(&Thread0_TCB,			// 控制块
											"appThread0",			// 进程名
											App_StartTask,		// 函数名
											0,								// 传入App的参数
											&Thread0_STK[0],	// 堆栈起始地址
											THREAD0_STK_SIZE,	// 堆栈大小
											THREAD0_PRIO,			// 任务优先级
											THREAD0_PRIO_VAL,	// 任务抢占阈值
											TX_NO_TIME_SLICE, // 不开启时间片
											TX_AUTO_START);		// 任务创建后立即启动
  }
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim){
  if (htim->Instance == TIM1) {
    HAL_IncTick();
  }
}

