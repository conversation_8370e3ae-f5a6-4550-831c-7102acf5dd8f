set(ASC_SECURITY_MODULE_ID "defender-iot-micro-agent")
set(ASC_FIRST_COLLECTION_INTERVAL 30)
set(ASC_HIGH_PRIORITY_INTERVAL 10)
set(ASC_MEDIUM_PRIORITY_INTERVAL 30)
set(ASC_LOW_PRIORITY_INTERVAL 60)

set(ASC_COLLECTOR_HEARTBEAT_ENABLED ON)
set(ASC_COLLECTOR_BASELINE_ENABLED OFF)
set(ASC_COLLECTOR_NETWORK_ACTIVITY_ENABLED OFF)
set(ASC_COLLECTOR_PROCESS_ENABLED OFF)
set(ASC_COLLECTOR_SYSTEM_INFORMATION_ENABLED OFF)
set(ASC_COLLECTOR_LISTENING_PORTS_ENABLED OFF)

set(ASC_COMPONENT_COMMAND_EXECUTOR OFF)
set(ASC_COMPONENT_CONFIGURATION OFF)
set(ASC_COMPONENT_SECURITY_MODULE OFF)

set(ASC_OPERATIONS_POOL_ENTRIES 50)
set(ASC_BASELINE_REPORT_POOL_ENTRIES 100)
set(ASC_LOG_REPORT_POOL_ENTRIES 100)
set(ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV4_OBJECTS_IN_CACHE 64)
set(ASC_COLLECTOR_NETWORK_ACTIVITY_MAX_IPV6_OBJECTS_IN_CACHE 64)
set(ASC_COLLECTOR_PROCESS_IN_CACHE 32)

set(ASC_DYNAMIC_MEMORY_ENABLED OFF)

set(ASC_BEST_EFFORT_EVENT_LOOP OFF)

set(ASC_LOG_TIMESTAMP_DEFAULT ON)

set(run_core_unittests OFF)
set(run_core_coverage OFF)
set(build_json_printer OFF)
set(FLATCC_NO_ASSERT OFF)
set(FLATCC_USE_GENERIC_ALIGNED_ALLOC ON)
set(ASC_FLATCC_JSON_PRINTER_OVERWRITE OFF)
set(ASC_COMPONENT_CORE_SUPPORTS_RESTART ON)