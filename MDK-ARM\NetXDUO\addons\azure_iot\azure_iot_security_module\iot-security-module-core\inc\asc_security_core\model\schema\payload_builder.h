#ifndef PAYLOAD_BUILDER_H
#define PAYLOAD_BUILDER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef PAYLOAD_READER_H
#include "payload_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#ifndef LOG_BUILDER_H
#include "log_builder.h"
#endif
#ifndef PROCESS_BUILDER_H
#include "process_builder.h"
#endif
#ifndef BASELINE_BUILDER_H
#include "baseline_builder.h"
#endif
#ifndef HEARTBEAT_BUILDER_H
#include "heartbeat_builder.h"
#endif
#ifndef LISTENING_PORTS_BUILDER_H
#include "listening_ports_builder.h"
#endif
#ifndef SYSTEM_INFORMATION_BUILDER_H
#include "system_information_builder.h"
#endif
#ifndef NETWORK_ACTIVITY_BUILDER_H
#include "network_activity_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

typedef flatbuffers_union_ref_t AzureIoTSecurity_Payload_union_ref_t;
typedef flatbuffers_union_vec_ref_t AzureIoTSecurity_Payload_union_vec_ref_t;
static AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_clone(flatbuffers_builder_t *B, AzureIoTSecurity_Payload_union_t t);

static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_NONE(void)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_NONE; uref.value = 0; return uref; }
static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_NetworkActivity(AzureIoTSecurity_NetworkActivity_ref_t ref)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_NetworkActivity; uref.value = ref; return uref; }
static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_SystemInformation(AzureIoTSecurity_SystemInformation_ref_t ref)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_SystemInformation; uref.value = ref; return uref; }
static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_ListeningPorts(AzureIoTSecurity_ListeningPorts_ref_t ref)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_ListeningPorts; uref.value = ref; return uref; }
static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_Heartbeat(AzureIoTSecurity_Heartbeat_ref_t ref)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_Heartbeat; uref.value = ref; return uref; }
static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_Baseline(AzureIoTSecurity_Baseline_ref_t ref)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_Baseline; uref.value = ref; return uref; }
static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_Process(AzureIoTSecurity_Process_ref_t ref)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_Process; uref.value = ref; return uref; }
static inline AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_as_Log(AzureIoTSecurity_Log_ref_t ref)
{ AzureIoTSecurity_Payload_union_ref_t uref; uref.type = AzureIoTSecurity_Payload_Log; uref.value = ref; return uref; }
__flatbuffers_build_union_vector(flatbuffers_, AzureIoTSecurity_Payload)

static AzureIoTSecurity_Payload_union_ref_t AzureIoTSecurity_Payload_clone(flatbuffers_builder_t *B, AzureIoTSecurity_Payload_union_t u)
{
    switch (u.type) {
    case 1: return AzureIoTSecurity_Payload_as_NetworkActivity(AzureIoTSecurity_NetworkActivity_clone(B, (AzureIoTSecurity_NetworkActivity_table_t)u.value));
    case 2: return AzureIoTSecurity_Payload_as_SystemInformation(AzureIoTSecurity_SystemInformation_clone(B, (AzureIoTSecurity_SystemInformation_table_t)u.value));
    case 3: return AzureIoTSecurity_Payload_as_ListeningPorts(AzureIoTSecurity_ListeningPorts_clone(B, (AzureIoTSecurity_ListeningPorts_table_t)u.value));
    case 4: return AzureIoTSecurity_Payload_as_Heartbeat(AzureIoTSecurity_Heartbeat_clone(B, (AzureIoTSecurity_Heartbeat_table_t)u.value));
    case 5: return AzureIoTSecurity_Payload_as_Baseline(AzureIoTSecurity_Baseline_clone(B, (AzureIoTSecurity_Baseline_table_t)u.value));
    case 6: return AzureIoTSecurity_Payload_as_Process(AzureIoTSecurity_Process_clone(B, (AzureIoTSecurity_Process_table_t)u.value));
    case 7: return AzureIoTSecurity_Payload_as_Log(AzureIoTSecurity_Log_clone(B, (AzureIoTSecurity_Log_table_t)u.value));
    default: return AzureIoTSecurity_Payload_as_NONE();
    }
}

#include "flatcc/flatcc_epilogue.h"
#endif /* PAYLOAD_BUILDER_H */
