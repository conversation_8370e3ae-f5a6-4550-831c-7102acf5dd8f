#ifndef BASELINE_JSON_PARSER_H
#define BASELINE_JSON_PARSER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_parser.h"
#include "flatcc/flatcc_prologue.h"

static const char *AzureIoTSecurity_Result_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);
static const char *AzureIoTSecurity_Severity_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);
static const char *AzureIoTSecurity_BaselineCheck_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *AzureIoTSecurity_Baseline_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result);
static const char *baseline_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *baseline_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
int *value_type, uint64_t *value, int *aggregate);
static const char *baseline_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate);

static const char *AzureIoTSecurity_Result_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_sign, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w < 0x4641494c00000000) { /* branch "FAIL" */
        if ((w & 0xffffffffff000000) == 0x4552524f52000000) { /* "ERROR" */
            buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 5, aggregate);
            if (buf != mark) {
                *value = UINT64_C(2), *value_sign = 0;
            } else {
                return unmatched;
            }
        } else { /* "ERROR" */
            return unmatched;
        } /* "ERROR" */
    } else { /* branch "FAIL" */
        if ((w & 0xffffffff00000000) == 0x5041535300000000) { /* "PASS" */
            buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 4, aggregate);
            if (buf != mark) {
                *value = UINT64_C(0), *value_sign = 0;
            } else {
                return unmatched;
            }
        } else { /* "PASS" */
            if ((w & 0xffffffff00000000) == 0x4641494c00000000) { /* "FAIL" */
                buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 4, aggregate);
                if (buf != mark) {
                    *value = UINT64_C(1), *value_sign = 0;
                } else {
                    return unmatched;
                }
            } else { /* "FAIL" */
                return unmatched;
            } /* "FAIL" */
        } /* "PASS" */
    } /* branch "FAIL" */
    return buf;
}

static const char *AzureIoTSecurity_Severity_parse_json_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_sign, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w < 0x494d504f5254414e) { /* branch "IMPORTAN" */
        if (w == 0x435249544943414c) { /* "CRITICAL" */
            buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 8, aggregate);
            if (buf != mark) {
                *value = UINT64_C(0), *value_sign = 0;
            } else {
                return unmatched;
            }
        } else { /* "CRITICAL" */
            return unmatched;
        } /* "CRITICAL" */
    } else { /* branch "IMPORTAN" */
        if (w < 0x494e464f524d4154) { /* branch "INFORMAT" */
            if (w == 0x494d504f5254414e) { /* descend "IMPORTAN" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xff00000000000000) == 0x5400000000000000) { /* "T" */
                    buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 1, aggregate);
                    if (buf != mark) {
                        *value = UINT64_C(1), *value_sign = 0;
                    } else {
                        return unmatched;
                    }
                } else { /* "T" */
                    return unmatched;
                } /* "T" */
            } else { /* descend "IMPORTAN" */
                return unmatched;
            } /* descend "IMPORTAN" */
        } else { /* branch "INFORMAT" */
            if (w == 0x494e464f524d4154) { /* descend "INFORMAT" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xffffffffff000000) == 0x494f4e414c000000) { /* "IONAL" */
                    buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 5, aggregate);
                    if (buf != mark) {
                        *value = UINT64_C(3), *value_sign = 0;
                    } else {
                        return unmatched;
                    }
                } else { /* "IONAL" */
                    return unmatched;
                } /* "IONAL" */
            } else { /* descend "INFORMAT" */
                if ((w & 0xffffffffffffff00) == 0x5741524e494e4700) { /* "WARNING" */
                    buf = flatcc_json_parser_match_constant(ctx, (mark = buf), end, 7, aggregate);
                    if (buf != mark) {
                        *value = UINT64_C(2), *value_sign = 0;
                    } else {
                        return unmatched;
                    }
                } else { /* "WARNING" */
                    return unmatched;
                } /* "WARNING" */
            } /* descend "INFORMAT" */
        } /* branch "INFORMAT" */
    } /* branch "IMPORTAN" */
    return buf;
}

static const char *AzureIoTSecurity_BaselineCheck_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 6)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w < 0x6964000000000000) { /* branch "id" */
            if (w == 0x6465736372697074) { /* descend "descript" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xffffff0000000000) == 0x696f6e0000000000) { /* "ion" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 3);
                    if (mark != buf) {
                        buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                        if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 3))) goto failed;
                        *pref = ref;
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "ion" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "ion" */
            } else { /* descend "descript" */
                if ((w & 0xffffffffff000000) == 0x6572726f72000000) { /* "error" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 5);
                    if (mark != buf) {
                        buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                        if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 2))) goto failed;
                        *pref = ref;
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "error" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "error" */
            } /* descend "descript" */
        } else { /* branch "id" */
            if (w < 0x72656d6564696174) { /* branch "remediat" */
                if ((w & 0xffff000000000000) == 0x6964000000000000) { /* "id" */
                    buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 2);
                    if (mark != buf) {
                        buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                        if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 0))) goto failed;
                        *pref = ref;
                    } else {
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    }
                } else { /* "id" */
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                } /* "id" */
            } else { /* branch "remediat" */
                if (w < 0x726573756c740000) { /* branch "result" */
                    if (w == 0x72656d6564696174) { /* descend "remediat" */
                        buf += 8;
                        w = flatcc_json_parser_symbol_part(buf, end);
                        if ((w & 0xffffff0000000000) == 0x696f6e0000000000) { /* "ion" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 3);
                            if (mark != buf) {
                                buf = flatcc_json_parser_build_string(ctx, buf, end, &ref);
                                if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 5))) goto failed;
                                *pref = ref;
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "ion" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "ion" */
                    } else { /* descend "remediat" */
                        buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                    } /* descend "remediat" */
                } else { /* branch "result" */
                    if (w == 0x7365766572697479) { /* "severity" */
                        buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 8);
                        if (mark != buf) {
                            int8_t val = 0;
                            static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                    AzureIoTSecurity_Severity_parse_json_enum,
                                    baseline_local_AzureIoTSecurity_json_parser_enum,
                                    baseline_global_json_parser_enum, 0 };
                            buf = flatcc_json_parser_int8(ctx, (mark = buf), end, &val);
                            if (mark == buf) {
                                buf = flatcc_json_parser_symbolic_int8(ctx, (mark = buf), end, symbolic_parsers, &val);
                                if (buf == mark || buf == end) goto failed;
                            }
                            if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                if (!(pval = flatcc_builder_table_add(ctx->ctx, 4, 1, 1))) goto failed;
                                flatbuffers_int8_write_to_pe(pval, val);
                            }
                        } else {
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        }
                    } else { /* "severity" */
                        if ((w & 0xffffffffffff0000) == 0x726573756c740000) { /* "result" */
                            buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 6);
                            if (mark != buf) {
                                int8_t val = 0;
                                static flatcc_json_parser_integral_symbol_f *symbolic_parsers[] = {
                                        AzureIoTSecurity_Result_parse_json_enum,
                                        baseline_local_AzureIoTSecurity_json_parser_enum,
                                        baseline_global_json_parser_enum, 0 };
                                buf = flatcc_json_parser_int8(ctx, (mark = buf), end, &val);
                                if (mark == buf) {
                                    buf = flatcc_json_parser_symbolic_int8(ctx, (mark = buf), end, symbolic_parsers, &val);
                                    if (buf == mark || buf == end) goto failed;
                                }
                                if (val != 0 || (ctx->flags & flatcc_json_parser_f_force_add)) {
                                    if (!(pval = flatcc_builder_table_add(ctx->ctx, 1, 1, 1))) goto failed;
                                    flatbuffers_int8_write_to_pe(pval, val);
                                }
                            } else {
                                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                            }
                        } else { /* "result" */
                            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                        } /* "result" */
                    } /* "severity" */
                } /* branch "result" */
            } /* branch "remediat" */
        } /* branch "id" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!flatcc_builder_check_required_field(ctx->ctx, 0)
    ) {
        buf = flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_required);
        goto failed;
    }
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_BaselineCheck_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_BaselineCheck_parse_json_table);
}

static const char *AzureIoTSecurity_Baseline_parse_json_table(flatcc_json_parser_t *ctx, const char *buf, const char *end, flatcc_builder_ref_t *result)
{
    int more;
    void *pval;
    flatcc_builder_ref_t ref, *pref;
    const char *mark;
    uint64_t w;

    *result = 0;
    if (flatcc_builder_start_table(ctx->ctx, 1)) goto failed;
    buf = flatcc_json_parser_object_start(ctx, buf, end, &more);
    while (more) {
        buf = flatcc_json_parser_symbol_start(ctx, buf, end);
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x626173656c696e65) { /* descend "baseline" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if ((w & 0xffffffffffffff00) == 0x5f636865636b7300) { /* "_checks" */
                buf = flatcc_json_parser_match_symbol(ctx, (mark = buf), end, 7);
                if (mark != buf) {
                    if (flatcc_builder_start_offset_vector(ctx->ctx)) goto failed;
                    buf = flatcc_json_parser_array_start(ctx, buf, end, &more);
                    while (more) {
                        buf = AzureIoTSecurity_BaselineCheck_parse_json_table(ctx, buf, end, &ref);
                        if (!ref || !(pref = flatcc_builder_extend_offset_vector(ctx->ctx, 1))) goto failed;
                        *pref = ref;
                        buf = flatcc_json_parser_array_end(ctx, buf, end, &more);
                    }
                    ref = flatcc_builder_end_offset_vector(ctx->ctx);
                    if (!ref || !(pref = flatcc_builder_table_add_offset(ctx->ctx, 0))) goto failed;
                    *pref = ref;
                } else {
                    buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
                }
            } else { /* "_checks" */
                buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
            } /* "_checks" */
        } else { /* descend "baseline" */
            buf = flatcc_json_parser_unmatched_symbol(ctx, buf, end);
        } /* descend "baseline" */
        buf = flatcc_json_parser_object_end(ctx, buf, end, &more);
    }
    if (ctx->error) goto failed;
    if (!(*result = flatcc_builder_end_table(ctx->ctx))) goto failed;
    return buf;
failed:
    return flatcc_json_parser_set_error(ctx, buf, end, flatcc_json_parser_error_runtime);
}

static inline int AzureIoTSecurity_Baseline_parse_json_as_root(flatcc_builder_t *B, flatcc_json_parser_t *ctx, const char *buf, size_t bufsiz, int flags, const char *fid)
{
    return flatcc_json_parser_table_as_root(B, ctx, buf, bufsiz, flags, fid, AzureIoTSecurity_Baseline_parse_json_table);
}

static const char *baseline_local_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    /* Scope has no enum / union types to look up. */
    return buf; /* unmatched; */
}

static const char *baseline_local_AzureIoTSecurity_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x5365766572697479) { /* "Severity" */
        buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 8);
        if (buf != mark) {
            buf = AzureIoTSecurity_Severity_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
        } else {
            return unmatched;
        }
    } else { /* "Severity" */
        if ((w & 0xffffffffffff0000) == 0x526573756c740000) { /* "Result" */
            buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 6);
            if (buf != mark) {
                buf = AzureIoTSecurity_Result_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
            } else {
                return unmatched;
            }
        } else { /* "Result" */
            return unmatched;
        } /* "Result" */
    } /* "Severity" */
    return buf;
}

static const char *baseline_global_json_parser_enum(flatcc_json_parser_t *ctx, const char *buf, const char *end,
        int *value_type, uint64_t *value, int *aggregate)
{
    const char *unmatched = buf;
    const char *mark;
    uint64_t w;

    w = flatcc_json_parser_symbol_part(buf, end);
    if (w == 0x417a757265496f54) { /* descend "AzureIoT" */
        buf += 8;
        w = flatcc_json_parser_symbol_part(buf, end);
        if (w == 0x5365637572697479) { /* descend "Security" */
            buf += 8;
            w = flatcc_json_parser_symbol_part(buf, end);
            if ((w & 0xffffffffffffff00) == 0x2e526573756c7400) { /* ".Result" */
                buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 7);
                if (buf != mark) {
                    buf = AzureIoTSecurity_Result_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                } else {
                    goto pfguard1;
                }
            } else { /* ".Result" */
                goto pfguard1;
            } /* ".Result" */
            goto endpfguard1;
pfguard1:
            if (w == 0x2e53657665726974) { /* descend ".Severit" */
                buf += 8;
                w = flatcc_json_parser_symbol_part(buf, end);
                if ((w & 0xff00000000000000) == 0x7900000000000000) { /* "y" */
                    buf = flatcc_json_parser_match_scope(ctx, (mark = buf), end, 1);
                    if (buf != mark) {
                        buf = AzureIoTSecurity_Severity_parse_json_enum(ctx, buf, end, value_type, value, aggregate);
                    } else {
                        return unmatched;
                    }
                } else { /* "y" */
                    return unmatched;
                } /* "y" */
            } else { /* descend ".Severit" */
                return unmatched;
            } /* descend ".Severit" */
endpfguard1:
            (void)0;
        } else { /* descend "Security" */
            return unmatched;
        } /* descend "Security" */
    } else { /* descend "AzureIoT" */
        return unmatched;
    } /* descend "AzureIoT" */
    return buf;
}

#include "flatcc/flatcc_epilogue.h"
#endif /* BASELINE_JSON_PARSER_H */
