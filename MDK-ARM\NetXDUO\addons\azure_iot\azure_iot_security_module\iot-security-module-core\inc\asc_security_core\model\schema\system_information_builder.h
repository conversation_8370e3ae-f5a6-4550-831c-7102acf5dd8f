#ifndef SYSTEM_INFORMATION_BUILDER_H
#define SYSTEM_INFORMATION_BUILDER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#ifndef SYSTEM_INFORMATION_READER_H
#include "system_information_reader.h"
#endif
#ifndef FLATBUFFERS_COMMON_BUILDER_H
#include "flatbuffers_common_builder.h"
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif

static const flatbuffers_voffset_t __AzureIoTSecurity_SystemInformation_required[] = { 0 };
typedef flatbuffers_ref_t AzureIoTSecurity_SystemInformation_ref_t;
static AzureIoTSecurity_SystemInformation_ref_t AzureIoTSecurity_SystemInformation_clone(flatbuffers_builder_t *B, AzureIoTSecurity_SystemInformation_table_t t);
__flatbuffers_build_table(flatbuffers_, AzureIoTSecurity_SystemInformation, 3)

#define __AzureIoTSecurity_SystemInformation_formal_args , flatbuffers_string_ref_t v0, flatbuffers_string_ref_t v1, flatbuffers_string_ref_t v2
#define __AzureIoTSecurity_SystemInformation_call_args , v0, v1, v2
static inline AzureIoTSecurity_SystemInformation_ref_t AzureIoTSecurity_SystemInformation_create(flatbuffers_builder_t *B __AzureIoTSecurity_SystemInformation_formal_args);
__flatbuffers_build_table_prolog(flatbuffers_, AzureIoTSecurity_SystemInformation, AzureIoTSecurity_SystemInformation_file_identifier, AzureIoTSecurity_SystemInformation_type_identifier)

__flatbuffers_build_string_field(0, flatbuffers_, AzureIoTSecurity_SystemInformation_os_info, AzureIoTSecurity_SystemInformation)
__flatbuffers_build_string_field(1, flatbuffers_, AzureIoTSecurity_SystemInformation_kernel_info, AzureIoTSecurity_SystemInformation)
__flatbuffers_build_string_field(2, flatbuffers_, AzureIoTSecurity_SystemInformation_hw_info, AzureIoTSecurity_SystemInformation)

static inline AzureIoTSecurity_SystemInformation_ref_t AzureIoTSecurity_SystemInformation_create(flatbuffers_builder_t *B __AzureIoTSecurity_SystemInformation_formal_args)
{
    if (AzureIoTSecurity_SystemInformation_start(B)
        || AzureIoTSecurity_SystemInformation_os_info_add(B, v0)
        || AzureIoTSecurity_SystemInformation_kernel_info_add(B, v1)
        || AzureIoTSecurity_SystemInformation_hw_info_add(B, v2)) {
        return 0;
    }
    return AzureIoTSecurity_SystemInformation_end(B);
}

static AzureIoTSecurity_SystemInformation_ref_t AzureIoTSecurity_SystemInformation_clone(flatbuffers_builder_t *B, AzureIoTSecurity_SystemInformation_table_t t)
{
    __flatbuffers_memoize_begin(B, t);
    if (AzureIoTSecurity_SystemInformation_start(B)
        || AzureIoTSecurity_SystemInformation_os_info_pick(B, t)
        || AzureIoTSecurity_SystemInformation_kernel_info_pick(B, t)
        || AzureIoTSecurity_SystemInformation_hw_info_pick(B, t)) {
        return 0;
    }
    __flatbuffers_memoize_end(B, t, AzureIoTSecurity_SystemInformation_end(B));
}

#include "flatcc/flatcc_epilogue.h"
#endif /* SYSTEM_INFORMATION_BUILDER_H */
