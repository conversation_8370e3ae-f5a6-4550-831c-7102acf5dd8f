#ifndef NETWORK_ACTIVITY_JSON_PRINTER_H
#define NETWORK_ACTIVITY_JSON_PRINTER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_printer.h"
#ifndef PROTOCOL_JSON_PRINTER_H
#include "protocol_json_printer.h"
#endif
#include "flatcc/flatcc_prologue.h"

static void AzureIoTSecurity_IPv4Addresses_print_json_struct(flatcc_json_printer_t *ctx, const void *p);
static void AzureIoTSecurity_IPv6Addresses_print_json_struct(flatcc_json_printer_t *ctx, const void *p);
static void AzureIoTSecurity_NetworkActivityCommon_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td);
static void AzureIoTSecurity_NetworkActivityV4_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td);
static void AzureIoTSecurity_NetworkActivityV6_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td);
static void AzureIoTSecurity_NetworkActivity_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td);

static void AzureIoTSecurity_IPv4Addresses_print_json_struct(flatcc_json_printer_t *ctx, const void *p)
{
    flatcc_json_printer_uint32_struct_field(ctx, 0, p, 0, "local_address", 13);
    flatcc_json_printer_uint32_struct_field(ctx, 1, p, 4, "remote_address", 14);
}

static inline int AzureIoTSecurity_IPv4Addresses_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_struct_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_IPv4Addresses_print_json_struct);
}

static void AzureIoTSecurity_IPv6Addresses_print_json_struct(flatcc_json_printer_t *ctx, const void *p)
{
    flatcc_json_printer_uint32_array_struct_field(ctx, 0, p, 0, "local_address", 13, 4);
    flatcc_json_printer_uint32_array_struct_field(ctx, 1, p, 16, "remote_address", 14, 4);
}

static inline int AzureIoTSecurity_IPv6Addresses_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_struct_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_IPv6Addresses_print_json_struct);
}

static void AzureIoTSecurity_NetworkActivityCommon_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td)
{
    flatcc_json_printer_uint16_field(ctx, td, 0, "local_port", 10, 0);
    flatcc_json_printer_uint16_field(ctx, td, 1, "remote_port", 11, 0);
    flatcc_json_printer_uint32_field(ctx, td, 2, "bytes_in", 8, 0);
    flatcc_json_printer_uint32_field(ctx, td, 3, "bytes_out", 9, 0);
    flatcc_json_printer_int8_enum_field(ctx, td, 4, "protocol", 8, 0, AzureIoTSecurity_Protocol_print_json_enum);
    flatcc_json_printer_uint16_field(ctx, td, 5, "process_id", 10, 0);
    flatcc_json_printer_uint16_field(ctx, td, 6, "user_id", 7, 0);
    flatcc_json_printer_string_field(ctx, td, 7, "executable", 10);
    flatcc_json_printer_string_field(ctx, td, 8, "commandline", 11);
}

static inline int AzureIoTSecurity_NetworkActivityCommon_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_table_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_NetworkActivityCommon_print_json_table);
}

static void AzureIoTSecurity_NetworkActivityV4_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td)
{
    flatcc_json_printer_struct_field(ctx, td, 0, "addresses", 9, AzureIoTSecurity_IPv4Addresses_print_json_struct);
    flatcc_json_printer_table_field(ctx, td, 1, "common", 6, AzureIoTSecurity_NetworkActivityCommon_print_json_table);
}

static inline int AzureIoTSecurity_NetworkActivityV4_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_table_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_NetworkActivityV4_print_json_table);
}

static void AzureIoTSecurity_NetworkActivityV6_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td)
{
    flatcc_json_printer_struct_field(ctx, td, 0, "addresses", 9, AzureIoTSecurity_IPv6Addresses_print_json_struct);
    flatcc_json_printer_table_field(ctx, td, 1, "common", 6, AzureIoTSecurity_NetworkActivityCommon_print_json_table);
}

static inline int AzureIoTSecurity_NetworkActivityV6_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_table_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_NetworkActivityV6_print_json_table);
}

static void AzureIoTSecurity_NetworkActivity_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td)
{
    flatcc_json_printer_table_vector_field(ctx, td, 0, "ipv4_activity", 13, AzureIoTSecurity_NetworkActivityV4_print_json_table);
    flatcc_json_printer_table_vector_field(ctx, td, 1, "ipv6_activity", 13, AzureIoTSecurity_NetworkActivityV6_print_json_table);
}

static inline int AzureIoTSecurity_NetworkActivity_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_table_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_NetworkActivity_print_json_table);
}

#include "flatcc/flatcc_epilogue.h"
#endif /* NETWORK_ACTIVITY_JSON_PRINTER_H */
