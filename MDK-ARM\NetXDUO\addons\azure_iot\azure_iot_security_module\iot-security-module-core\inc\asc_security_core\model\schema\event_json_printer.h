#ifndef EVENT_JSON_PRINTER_H
#define EVENT_JSON_PRINTER_H

/* Generated by flatcc 0.6.1-dev FlatBuffers schema compiler for C by dvide.com */

#include "flatcc/flatcc_json_printer.h"
#ifndef PAYLOAD_JSON_PRINTER_H
#include "payload_json_printer.h"
#endif
#ifndef UUID_JSON_PRINTER_H
#include "uuid_json_printer.h"
#endif
#include "flatcc/flatcc_prologue.h"

static void AzureIoTSecurity_Event_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td);

static void AzureIoTSecurity_Event_print_json_table(flatcc_json_printer_t *ctx, flatcc_json_printer_table_descriptor_t *td)
{
    flatcc_json_printer_struct_field(ctx, td, 0, "id", 2, AzureIoTSecurity_UUID_print_json_struct);
    flatcc_json_printer_uint32_field(ctx, td, 1, "time", 4, 0);
    flatcc_json_printer_uint32_field(ctx, td, 2, "collection_interval", 19, 0);
    flatcc_json_printer_union_field(ctx, td, 4, "payload", 7, AzureIoTSecurity_Payload_print_json_union_type, AzureIoTSecurity_Payload_print_json_union);
}

static inline int AzureIoTSecurity_Event_print_json_as_root(flatcc_json_printer_t *ctx, const void *buf, size_t bufsiz, const char *fid)
{
    return flatcc_json_printer_table_as_root(ctx, buf, bufsiz, fid, AzureIoTSecurity_Event_print_json_table);
}

#include "flatcc/flatcc_epilogue.h"
#endif /* EVENT_JSON_PRINTER_H */
