#ifndef PROCESS_EVENT_READER_H
#define PROCESS_EVENT_READER_H

/* Generated by flatcc 0.6.1-dev <PERSON><PERSON>uffers schema compiler for C by dvide.com */

#ifndef FLATBUFFERS_COMMON_READER_H
#include "flatbuffers_common_reader.h"
#endif
#include "flatcc/flatcc_flatbuffers.h"
#ifndef __alignas_is_defined
#include <stdalign.h>
#endif
#include "flatcc/flatcc_prologue.h"
#ifndef flatbuffers_identifier
#define flatbuffers_identifier 0
#endif
#ifndef flatbuffers_extension
#define flatbuffers_extension ".bin"
#endif



typedef int8_t AzureIoTSecurity_ProcessEvent_enum_t;
__flatbuffers_define_integer_type(AzureIoTSecurity_ProcessEvent, AzureIoTSecurity_ProcessEvent_enum_t, 8)
#define AzureIoTSecurity_ProcessEvent_FORK ((AzureIoTSecurity_ProcessEvent_enum_t)INT8_C(0))
#define AzureIoTSecurity_ProcessEvent_EXEC ((AzureIoTSecurity_ProcessEvent_enum_t)INT8_C(1))
#define AzureIoTSecurity_ProcessEvent_EXIT ((AzureIoTSecurity_ProcessEvent_enum_t)INT8_C(2))

static inline const char *AzureIoTSecurity_ProcessEvent_name(AzureIoTSecurity_ProcessEvent_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_ProcessEvent_FORK: return "FORK";
    case AzureIoTSecurity_ProcessEvent_EXEC: return "EXEC";
    case AzureIoTSecurity_ProcessEvent_EXIT: return "EXIT";
    default: return "";
    }
}

static inline int AzureIoTSecurity_ProcessEvent_is_known_value(AzureIoTSecurity_ProcessEvent_enum_t value)
{
    switch (value) {
    case AzureIoTSecurity_ProcessEvent_FORK: return 1;
    case AzureIoTSecurity_ProcessEvent_EXEC: return 1;
    case AzureIoTSecurity_ProcessEvent_EXIT: return 1;
    default: return 0;
    }
}




#include "flatcc/flatcc_epilogue.h"
#endif /* PROCESS_EVENT_READER_H */
